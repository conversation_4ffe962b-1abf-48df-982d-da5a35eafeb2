apiVersion: v1
kind: ServiceAccount
metadata:
  name: trivy-scanner
  namespace: security
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: trivy-scanner
  namespace: security
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["create", "get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: trivy-scanner
  namespace: security
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: trivy-scanner
subjects:
- kind: ServiceAccount
  name: trivy-scanner
  namespace: security
