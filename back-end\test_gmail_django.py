#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test Gmail SMTP avec Django
"""

import os
import sys
import django

# Configuration des variables d'environnement pour Gmail SMTP
os.environ['DJANGO_SETTINGS_MODULE'] = 'SystemeRH_v2.settings'
os.environ['EMAIL_BACKEND'] = 'django.core.mail.backends.smtp.EmailBackend'
os.environ['EMAIL_PROVIDER'] = 'gmail'
os.environ['EMAIL_HOST'] = 'smtp.gmail.com'
os.environ['EMAIL_PORT'] = '587'
os.environ['EMAIL_USE_TLS'] = 'True'
os.environ['EMAIL_HOST_USER'] = '<EMAIL>'
os.environ['EMAIL_HOST_PASSWORD'] = 'vyjv svsb cdvk aixv'
os.environ['DEFAULT_FROM_EMAIL'] = 'RH System <<EMAIL>>'
os.environ['SECRET_KEY'] = 'django-insecure-test-key-for-gmail-smtp'
os.environ['DEBUG'] = 'True'
os.environ['ALLOWED_HOSTS'] = 'localhost,127.0.0.1'

# Configuration base de données SQLite pour test
os.environ['DATABASE_ENGINE'] = 'django.db.backends.sqlite3'
os.environ['DATABASE_NAME'] = 'test_db.sqlite3'
os.environ['DATABASE_USER'] = ''
os.environ['DATABASE_PASSWORD'] = ''
os.environ['DATABASE_HOST'] = ''
os.environ['DATABASE_PORT'] = ''

# Initialiser Django
try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from django.core.mail import send_mail
from django.conf import settings

def test_gmail_smtp():
    """Test de la configuration Gmail SMTP avec Django"""
    
    print("🔧 Configuration Django Email:")
    print(f"   EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"   EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', 'Non configuré')}")
    print(f"   EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'Non configuré')}")
    print(f"   EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Non configuré')}")
    print(f"   EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', 'Non configuré')}")
    print(f"   DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()

    print("📧 Test envoi email Django + Gmail SMTP...")
    
    try:
        # Test 1: Email simple
        result = send_mail(
            subject='✅ Test RH System - Django + Gmail SMTP',
            message='''Félicitations ! 

Django avec Gmail SMTP fonctionne parfaitement !

Votre système RH peut maintenant envoyer des emails en production :
- Notifications utilisateurs
- Réinitialisations de mot de passe  
- Alertes administrateur
- Rapports automatiques

Configuration testée avec succès !''',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
        
        print(f"✅ Email simple envoyé avec succès ! Résultat: {result}")
        
        # Test 2: Email HTML
        from django.core.mail import EmailMessage
        
        html_content = """
        <html>
        <body>
            <h2>🎉 Test RH System - Django + Gmail SMTP</h2>
            <p><strong>Félicitations !</strong> La configuration Gmail SMTP fonctionne parfaitement avec Django.</p>
            
            <h3>✅ Tests réussis :</h3>
            <ul>
                <li>Connexion SMTP Gmail</li>
                <li>Authentification avec mot de passe d'application</li>
                <li>Envoi d'email via Django</li>
                <li>Support HTML</li>
            </ul>
            
            <h3>🚀 Fonctionnalités disponibles :</h3>
            <ul>
                <li>Notifications utilisateurs</li>
                <li>Réinitialisations de mot de passe</li>
                <li>Alertes administrateur</li>
                <li>Rapports automatiques</li>
            </ul>
            
            <p><em>Votre système RH est prêt pour la production !</em></p>
            
            <hr>
            <small>Email généré automatiquement par le système RH</small>
        </body>
        </html>
        """
        
        email = EmailMessage(
            subject='🎉 Test RH System - Django + Gmail SMTP (HTML)',
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=['<EMAIL>'],
        )
        email.content_subtype = "html"
        email.send()
        
        print("✅ Email HTML envoyé avec succès !")
        print()
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("📧 Vérifiez votre boîte email Gmail")
        print("🚀 Django + Gmail SMTP est parfaitement configuré !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test Gmail SMTP avec Django")
    print("=" * 50)
    
    success = test_gmail_smtp()
    
    print("=" * 50)
    if success:
        print("✅ Configuration Gmail SMTP validée !")
        print("🎯 Prêt pour la production")
    else:
        print("❌ Problème de configuration détecté")
        print("🔧 Vérifiez les paramètres Gmail SMTP")
