#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test complet de toutes les fonctions email avec Gmail SMTP
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')

try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from django.core.mail import send_mail, EmailMessage
from django.conf import settings
from Rh_app.utils import send_email_notification, get_admin_emails
from Rh_app.models import User

def test_django_email_system():
    """Test du système email Django de base"""
    print("\n🔧 Test du système email Django de base")
    print("-" * 50)
    
    try:
        print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
        print(f"EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', 'Non configuré')}")
        print(f"EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'Non configuré')}")
        print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Non configuré')}")
        print(f"EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', 'Non configuré')}")
        print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
        
        # Test envoi simple
        result = send_mail(
            subject='Test Django Email System - Gmail SMTP',
            message='Test du système email Django avec Gmail SMTP.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
        
        print(f"✅ Email Django envoyé avec succès ! Résultat: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur Django email: {e}")
        return False

def test_utils_email_notification():
    """Test de la fonction send_email_notification"""
    print("\n📧 Test de send_email_notification")
    print("-" * 50)
    
    try:
        result = send_email_notification(
            subject='Test Utils Email Notification - Gmail SMTP',
            message='Test de la fonction send_email_notification avec Gmail SMTP.',
            recipient_list=['<EMAIL>'],
            email_type='test',
            force_send=True
        )
        
        print(f"✅ Email utils envoyé avec succès ! Résultat: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur utils email: {e}")
        return False

def test_forgot_password_api():
    """Test de l'API forgot-password"""
    print("\n🔑 Test de l'API forgot-password")
    print("-" * 50)
    
    try:
        url = "http://localhost:8000/auth/forgot-password/"
        data = {"email": "<EMAIL>"}
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ API forgot-password fonctionne !")
            return True
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur API forgot-password: {e}")
        return False

def test_admin_emails():
    """Test de récupération des emails admin"""
    print("\n👥 Test de get_admin_emails")
    print("-" * 50)
    
    try:
        admin_emails = get_admin_emails()
        print(f"Emails admin trouvés: {admin_emails}")
        
        if admin_emails:
            print("✅ Emails admin récupérés avec succès !")
            return True
        else:
            print("⚠️ Aucun email admin trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur get_admin_emails: {e}")
        return False

def test_html_email():
    """Test d'envoi d'email HTML"""
    print("\n🎨 Test d'email HTML")
    print("-" * 50)
    
    try:
        html_content = """
        <html>
        <body>
            <h2>🎉 Test Email HTML - Gmail SMTP</h2>
            <p><strong>Félicitations !</strong> L'email HTML fonctionne parfaitement.</p>
            
            <h3>✅ Tests validés :</h3>
            <ul>
                <li>Configuration Gmail SMTP</li>
                <li>Système email Django</li>
                <li>Fonction send_email_notification</li>
                <li>API forgot-password</li>
                <li>Email HTML</li>
            </ul>
            
            <p><em>Votre système RH est opérationnel !</em></p>
            
            <hr>
            <small>Email généré automatiquement par le test complet</small>
        </body>
        </html>
        """
        
        email = EmailMessage(
            subject='🎨 Test Email HTML - Gmail SMTP',
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=['<EMAIL>'],
        )
        email.content_subtype = "html"
        email.send()
        
        print("✅ Email HTML envoyé avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur email HTML: {e}")
        return False

def test_mission_notification():
    """Test de notification de mission (simulation)"""
    print("\n📋 Test de notification de mission")
    print("-" * 50)
    
    try:
        # Simuler une notification de mission
        subject = "Test Mission Assignée - Gmail SMTP"
        message = """Bonjour,

Une mission de test vous a été assignée :

Titre : Test Gmail SMTP Integration
Description : Vérifier que toutes les notifications de mission fonctionnent avec Gmail SMTP
Date d'échéance : Test

Veuillez compléter cette mission avant la date limite.

Cordialement,
L'équipe RH"""

        result = send_email_notification(
            subject=subject,
            message=message,
            recipient_list=['<EMAIL>'],
            email_type='mission_created',
            reference_id=999
        )
        
        print(f"✅ Notification mission envoyée ! Résultat: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur notification mission: {e}")
        return False

def run_all_tests():
    """Exécuter tous les tests"""
    print("🚀 TEST COMPLET DE TOUTES LES FONCTIONS EMAIL")
    print("=" * 60)
    
    tests = [
        ("Django Email System", test_django_email_system),
        ("Utils Email Notification", test_utils_email_notification),
        ("Forgot Password API", test_forgot_password_api),
        ("Admin Emails", test_admin_emails),
        ("HTML Email", test_html_email),
        ("Mission Notification", test_mission_notification),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("📧 Vérifiez votre boîte email Gmail")
        print("🚀 Système email entièrement opérationnel avec Gmail SMTP")
    else:
        print("⚠️ Certains tests ont échoué")
        print("🔧 Vérifiez la configuration et les logs")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
