apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-init-script
  namespace: rh-system
data:
  init.sh: |
    #!/bin/bash

    # Attendre que la base de données soit prête
    echo 'Waiting for PostgreSQL to start...'
    until pg_isready -h postgres-db -p 5432; do
      echo 'PostgreSQL is unavailable - sleeping'
      sleep 1
    done
    echo 'PostgreSQL started!'

    # Appliquer les migrations
    echo 'Applying migrations...'
    python manage.py migrate --noinput

    # Créer un superutilisateur si nécessaire
    echo 'Creating superuser if needed...'
    python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='wael').exists() or User.objects.create_superuser('wael', '<EMAIL>', 'Abidos\$123'); print('Superuser created or already exists.')"

    # Collecter les fichiers statiques
    echo 'Collecting static files...'
    python manage.py collectstatic --noinput

    # Vérifier que les fichiers statiques ont été collectés
    echo 'Verifying static files...'
    if [ -d "static/admin" ]; then
        echo 'Static files for admin collected successfully'
        ls -la static/admin
    else
        echo 'Static files for admin not found, creating directory'
        mkdir -p static/admin

        # Copier les fichiers statiques de l'admin depuis Django
        echo 'Copying admin static files from Django...'
        python -c "import os, django, shutil; from django.contrib import admin; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings'); django.setup(); admin_static_dir = os.path.join(os.path.dirname(admin.__file__), 'static', 'admin'); print(f'Copying admin static files from {admin_static_dir}'); os.makedirs('static/admin', exist_ok=True); [shutil.copy2(os.path.join(root, file), os.path.join('static/admin', os.path.relpath(os.path.join(root, file), admin_static_dir))) for root, dirs, files in os.walk(admin_static_dir) for file in files]"
    fi

    # Créer un fichier pour importer la configuration d'administration
    echo 'Creating admin configuration...'
    cat > SystemeRH_v2/admin_init.py << 'EOF'
    from django.conf import settings
    try:
        # Importer la configuration d'administration
        from SystemeRH_v2 import admin_settings
        print("Admin settings loaded successfully")
    except Exception as e:
        print(f"Error loading admin settings: {e}")
    EOF

    # Créer le fichier admin_init.py
    echo 'Creating admin_init.py...'
    cat > SystemeRH_v2/admin_init.py << 'EOF'
    from django.conf import settings
    try:
        # Importer la configuration d'administration
        from SystemeRH_v2 import admin_settings
        print("Admin settings loaded successfully")
    except Exception as e:
        print(f"Error loading admin settings: {e}")
    EOF

    # Configurer django-prometheus dans settings.py
    echo 'Configuring django-prometheus in settings.py...'
    python -c "
    import os
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')
    import django
    django.setup()
    from django.conf import settings

    # Vérifier si django_prometheus est dans INSTALLED_APPS
    if 'django_prometheus' not in settings.INSTALLED_APPS:
        settings_path = '/app/SystemeRH_v2/settings.py'
        with open(settings_path, 'r') as f:
            content = f.read()

        # Ajouter django_prometheus aux INSTALLED_APPS
        content = content.replace(
            'INSTALLED_APPS = [',
            'INSTALLED_APPS = [\\n    \"django_prometheus\",')

        # Ajouter les middlewares Prometheus
        content = content.replace(
            'MIDDLEWARE = [',
            'MIDDLEWARE = [\\n    \"django_prometheus.middleware.PrometheusBeforeMiddleware\",')

        content = content.replace(
            ']  # Middleware pour vérifier les missions en retard',
            ',\\n    \"django_prometheus.middleware.PrometheusAfterMiddleware\"\\n]  # Middleware pour vérifier les missions en retard')

        # Écrire les modifications
        with open(settings_path, 'w') as f:
            f.write(content)

        print('django-prometheus configured in settings.py')
    else:
        print('django-prometheus already configured in settings.py')
    "

    # Appliquer le patch pour ajouter le modèle Message
    echo 'Applying Message model patch...'
    if [ -f "/scripts/add_message_model.sh" ]; then
        bash /scripts/add_message_model.sh
        echo 'Message model patch applied'
    else
        echo 'Message model patch file not found'
    fi

    # Appliquer le correctif pour la fonctionnalité de chat
    echo 'Applying message fix...'
    # Créer le fichier views_message.py dans le répertoire Rh_app
    if [ -d "/app/message_view_fix" ]; then
        cp /app/message_view_fix/views_message.py /app/Rh_app/views_message.py
        echo 'Message fix applied'
    else
        echo 'Message view fix directory not found'
    fi

    # Mettre à jour le fichier urls.py pour utiliser la nouvelle vue
    if [ -f "/app/Rh_app/urls.py" ]; then
        # Supprimer l'importation de MessageViewSet du fichier views.py
        sed -i 's/MessageViewSet, //g' /app/Rh_app/urls.py

        # Ajouter l'importation de MessageViewSet depuis views_message.py
        if ! grep -q "from .views_message import MessageViewSet" /app/Rh_app/urls.py; then
            sed -i '/from .views import (/a\    from .views_message import MessageViewSet' /app/Rh_app/urls.py
            echo 'Updated urls.py to import MessageViewSet from views_message.py'
        fi

        # Corriger l'enregistrement de MessageViewSet avec basename
        sed -i "s/router.register(r'messages', MessageViewSet)/router.register(r'messages', MessageViewSet, basename='message')/g" /app/Rh_app/urls.py
        echo 'Fixed MessageViewSet registration with basename'
    fi

    # Supprimer la classe MessageViewSet du fichier views.py
    if [ -f "/app/Rh_app/views.py" ]; then
        if grep -q "class MessageViewSet" /app/Rh_app/views.py; then
            sed -i '/class MessageViewSet/,/return Response(contact_data)/c\# La classe MessageViewSet a été déplacée vers views_message.py' /app/Rh_app/views.py
            echo 'Removed MessageViewSet class from views.py'
        fi
    fi

    # Démarrer le serveur
    echo 'Starting server...'
    python manage.py runserver 0.0.0.0:8000
