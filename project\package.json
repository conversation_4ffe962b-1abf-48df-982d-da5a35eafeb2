{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --emptyOutDir", "build:debug": "vite build --debug --emptyOutDir", "lint": "eslint .", "preview": "vite preview", "postinstall": "npm run setup-types || echo 'Setup types failed but continuing'", "setup-types": "npm i -D @types/node && npm i drizzle-orm drizzle-zod zod || echo 'Some dependencies may be missing but continuing'", "health": "echo 'Frontend health check passed'", "docker:local": "docker-compose --env-file .env.local up --build", "docker:prod": "docker-compose --env-file .env.production up -d", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "docker:logs": "docker-compose logs -f", "docker:rebuild": "docker-compose down && docker-compose --env-file .env.local up --build --force-recreate"}, "dependencies": {"@radix-ui/react-toast": "^1.2.11", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.74.9", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.7.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "moment": "^2.30.1", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "tailwind-merge": "^3.2.0", "zod": "^3.24.4", "events": "^3.3.0", "util": "^0.12.5", "buffer": "^6.0.3", "zipkin": "^0.22.0", "zipkin-instrumentation-axios": "^0.1.0", "zipkin-instrumentation-fetch": "^0.22.0", "zipkin-transport-http": "^0.22.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.17", "@types/react": "^18.3.20", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18.3.6", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}