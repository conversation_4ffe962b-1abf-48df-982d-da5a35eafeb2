# Generated by Django 5.2 on 2025-05-02 14:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0005_internship_description_internship_title_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('date_time', models.DateTimeField()),
                ('duration_minutes', models.IntegerField(default=60)),
                ('location_type', models.CharField(choices=[('online', 'En ligne'), ('office', 'Bureau'), ('external', 'Lieu externe')], default='office', max_length=20)),
                ('location_details', models.Char<PERSON><PERSON>(blank=True, max_length=200, null=True)),
                ('meeting_link', models.URLField(blank=True, null=True)),
                ('status', models.CharField(choices=[('scheduled', 'Planifié'), ('completed', 'Terminé'), ('cancelled', 'Annulé')], default='scheduled', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('organizer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_meetings', to=settings.AUTH_USER_MODEL)),
                ('participants', models.ManyToManyField(related_name='meetings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
