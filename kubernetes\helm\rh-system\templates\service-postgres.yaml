{{- if .Values.postgresql.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.postgresql.name }}
  namespace: rh-system
  labels:
    app: {{ .Values.postgresql.name }}
spec:
  selector:
    app: {{ .Values.postgresql.name }}
  ports:
  - port: {{ .Values.postgresql.service.port }}
    targetPort: {{ .Values.postgresql.service.port }}
    protocol: TCP
    name: postgres
  type: {{ .Values.postgresql.service.type }}
{{- end }}
