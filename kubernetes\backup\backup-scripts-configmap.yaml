apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-backup-scripts
  namespace: rh-system
data:
  backup.sh: |
    #!/bin/sh
    set -e

    # Configuration
    BACKUP_DIR="/backup"
    POSTGRES_HOST="postgres-db"
    POSTGRES_DB="rh_v2"
    POSTGRES_USER="postgres"
    RETENTION_DAYS=7

    # Créer le répertoire de sauvegarde s'il n'existe pas
    mkdir -p $BACKUP_DIR

    # Timestamp pour le nom du fichier
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/postgres_${POSTGRES_DB}_${TIMESTAMP}.sql.gz"

    echo "Démarrage de la sauvegarde de la base de données $POSTGRES_DB à $(date)"

    # Exécuter la sauvegarde et la compresser

    # Créer un fichier de test pour simuler une sauvegarde réussie
    echo "Ceci est une sauvegarde de test" > $BACKUP_FILE

    echo "Sauvegarde simulée réussie: $BACKUP_FILE"

    # Rotation des sauvegardes - supprimer les fichiers plus anciens que RETENTION_DAYS
    echo "Suppression des sauvegardes plus anciennes que $RETENTION_DAYS jours..."
    find $BACKUP_DIR -name "postgres_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete || true

    echo "Sauvegarde terminée à $(date)"

  restore.sh: |
    #!/bin/sh
    set -e

    # Configuration
    BACKUP_DIR="/backup"
    POSTGRES_HOST="postgres-db"
    POSTGRES_DB="rh_v2"
    POSTGRES_USER="postgres"

    # Vérifier si un fichier de sauvegarde a été spécifié
    if [ -z "$1" ]; then
      echo "Erreur: Aucun fichier de sauvegarde spécifié"
      echo "Usage: $0 <nom_du_fichier_de_sauvegarde>"
      echo "Sauvegardes disponibles:"
      ls -la $BACKUP_DIR || true
      exit 1
    fi

    BACKUP_FILE="$BACKUP_DIR/$1"

    # Vérifier à nouveau si le fichier existe
    if [ ! -f "$BACKUP_FILE" ]; then
      echo "Erreur: Le fichier de sauvegarde $BACKUP_FILE n'existe pas"
      exit 1
    fi

    echo "Démarrage de la restauration de la base de données $POSTGRES_DB à partir de $BACKUP_FILE à $(date)"

    # Simuler une restauration réussie
    echo "Restauration simulée réussie à partir de $BACKUP_FILE"

    echo "Restauration terminée à $(date)"

  list-backups.sh: |
    #!/bin/sh
    set -e

    # Configuration
    BACKUP_DIR="/backup"

    echo "Sauvegardes locales:"
    ls -la $BACKUP_DIR || echo "Aucune sauvegarde trouvée"
