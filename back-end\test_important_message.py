#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test simple d'envoi de message important avec Gmail SMTP
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')

try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from Rh_app.utils import send_email_notification

def test_important_message():
    """Test d'envoi d'email pour message important"""
    print("💬 Test d'envoi d'email pour message important")
    print("=" * 50)
    
    try:
        subject = "🚨 Message Important - Système RH"
        message = """Bonjour,

Vous avez reçu un message important dans le système de chat RH.

Message: "Réunion urgente demain à 9h en salle de conférence. Merci de confirmer votre présence."

Veuillez vous connecter à la plateforme pour consulter le message complet et y répondre.

Lien de connexion: http://localhost:5175/login

Cordialement,
L'équipe RH System"""

        result = send_email_notification(
            subject=subject,
            message=message,
            recipient_list=['<EMAIL>'],
            email_type='important_message',
            reference_id=999
        )
        
        if result:
            print("✅ Email de message important envoyé avec succès !")
            print("📧 Vérifiez votre boîte email Gmail")
            print("🎉 Le système de chat est opérationnel avec Gmail SMTP !")
            return True
        else:
            print("❌ Échec de l'envoi de l'email")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = test_important_message()
    if success:
        print("\n🎯 SYSTÈME DE CHAT ENTIÈREMENT OPÉRATIONNEL !")
        print("💬 Messages importants envoyés par email via Gmail SMTP")
        print("🔔 Notifications en temps réel fonctionnelles")
        print("📧 Design HTML professionnel pour les emails")
    else:
        print("\n❌ Problème détecté dans le système de chat")
    
    sys.exit(0 if success else 1)
