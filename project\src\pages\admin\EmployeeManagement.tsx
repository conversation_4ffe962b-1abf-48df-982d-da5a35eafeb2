import React, { useState, useEffect } from "react";
import { API_BASE_URL } from "../../config/constants";
import { useAuth } from "../../contexts/AuthContext";
import Button from "../../components/ui/Button";
import LoadingSpinner from "../../components/ui/LoadingSpinner";
import { UsersIcon, UserIcon } from "lucide-react";

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type: string;
  leave_balance: number;
  profile_image?: string;
  github_profile?: string;
  total_hours_worked?: number;
}

interface WorkHour {
  id: number;
  date: string;
  hours_worked: number;
  description?: string;
  created_at: string;
  user: number;
}

const EmployeeManagement: React.FC = () => {
  const { token } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState("employee");

  useEffect(() => {
    // Check if there's a filter parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const filterParam = urlParams.get('filter');
    if (filterParam && ['employee', 'intern', 'admin', 'all'].includes(filterParam)) {
      setFilterType(filterParam);
    } else {
      // Par défaut, afficher uniquement les employés
      setFilterType('employee');
    }

    fetchUsers();
  }, [token, filterType]);

  const fetchUsers = async () => {
    if (!token) return;

    try {
      let url = `${API_BASE_URL}/users/`;
      if (filterType !== "all") {
        url += `?user_type=${filterType}`;
      }

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }

      const data = await response.json();

      // Récupérer les heures de travail pour chaque utilisateur (sauf les stagiaires)
      const usersWithWorkHours = await Promise.all(
        data.map(async (user: User) => {
          // Les stagiaires n'ont pas d'heures de travail
          if (user.user_type === 'intern') {
            return {
              ...user,
              total_hours_worked: 0
            };
          }

          try {
            const workHoursResponse = await fetch(
              `${API_BASE_URL}/work-hours/?user=${user.id}`,
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (workHoursResponse.ok) {
              const workHoursData = await workHoursResponse.json();

              // Calculer le total des heures travaillées
              const totalHours = workHoursData.reduce(
                (sum: number, entry: WorkHour) =>
                  sum + parseFloat(entry.hours_worked.toString()),
                0
              );

              return {
                ...user,
                total_hours_worked: parseFloat(totalHours.toFixed(2))
              };
            }
            return user;
          } catch (error) {
            console.error(`Error fetching work hours for user ${user.id}:`, error);
            return user;
          }
        })
      );

      setUsers(usersWithWorkHours);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const getUserTypeBadge = (userType: string) => {
    switch (userType) {
      case "admin":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Admin
          </span>
        );
      case "employee":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Employee
          </span>
        );
      case "intern":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            Intern
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return <LoadingSpinner fullScreen message="Loading users..." />;
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-semibold text-gray-900 mb-6">
        {filterType === "employee"
          ? "Employee Management"
          : filterType === "intern"
            ? "Intern Management"
            : filterType === "admin"
              ? "Admin Management"
              : "User Management"}
      </h1>

      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2 sm:mb-0">
              {filterType === "all"
                ? "All Users"
                : filterType === "employee"
                  ? "Employees"
                  : filterType === "intern"
                    ? "Interns"
                    : "Admins"}
            </h2>
            <div className="mt-2 sm:mt-0">
              <label htmlFor="user-type-filter" className="sr-only">Filter by user type</label>
              <select
                id="user-type-filter"
                aria-label="Filter by user type"
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={filterType}
                onChange={(e) => {
                  setFilterType(e.target.value);
                  // Mettre à jour l'URL avec le nouveau filtre
                  const url = new URL(window.location.href);
                  url.searchParams.set('filter', e.target.value);
                  window.history.pushState({}, '', url.toString());
                }}
              >
                <option value="employee">Employees</option>
                <option value="intern">Interns</option>
                <option value="admin">Admins</option>
                <option value="all">All Users</option>
              </select>
            </div>
          </div>
        </div>

        {users.length === 0 ? (
          <div className="p-6 text-center">
            <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No users found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {`No ${filterType}s found.`}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    User
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Type
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Leave Balance
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Work Hours
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden">
                          {user.profile_image ? (
                            <img
                              src={user.profile_image.startsWith('http') ? user.profile_image : `${API_BASE_URL}/media/${user.profile_image}`}
                              alt={`${user.first_name || user.username}'s profile`}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                // Fallback en cas d'erreur de chargement de l'image
                                e.currentTarget.onerror = null;
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.parentElement!.classList.add('bg-blue-100', 'flex', 'items-center', 'justify-center');
                                const span = document.createElement('span');
                                span.className = 'text-blue-800 font-semibold';
                                span.textContent = `${user.first_name ? user.first_name.charAt(0) : user.username.charAt(0)}${user.last_name ? user.last_name.charAt(0) : ""}`;
                                e.currentTarget.parentElement!.appendChild(span);
                              }}
                            />
                          ) : (
                            <div className="h-full w-full bg-blue-100 flex items-center justify-center">
                              <span className="text-blue-800 font-semibold">
                                {user.first_name
                                  ? user.first_name.charAt(0)
                                  : user.username.charAt(0)}
                                {user.last_name ? user.last_name.charAt(0) : ""}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.first_name && user.last_name
                              ? `${user.first_name} ${user.last_name}`
                              : user.username}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getUserTypeBadge(user.user_type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.user_type === 'intern' ? (
                        <span className="text-gray-400">N/A</span>
                      ) : (
                        `${user.leave_balance} days`
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.user_type === 'intern' ? (
                        <span className="text-gray-400">N/A</span>
                      ) : user.total_hours_worked !== undefined ? (
                        <div className="flex items-center">
                          <span className="font-medium">{user.total_hours_worked}</span>
                          <span className="ml-1">hours</span>
                        </div>
                      ) : (
                        <span className="text-gray-400">No data</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          icon={<UserIcon className="h-4 w-4" />}
                          onClick={() =>
                            window.open(`/admine/user/${user.id}`, "_blank")
                          }
                        >
                          View Profile
                        </Button>
                        {user.user_type !== 'intern' && (
                          <Button
                            variant="primary"
                            size="sm"
                            onClick={() =>
                              window.open(
                                `/admine/user/${user.id}/work-hours`,
                                "_blank"
                              )
                            }
                          >
                            Work Hours
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeManagement;
