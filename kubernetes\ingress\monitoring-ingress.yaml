apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "selfsigned-issuer"
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - zipkin.rh-system.local
    - prometheus.rh-system.local
    - grafana.rh-system.local
    secretName: monitoring-tls
  rules:
  # Zipkin tracing interface
  - host: zipkin.rh-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: zipkin
            port:
              number: 9411

  # Prometheus monitoring interface
  - host: prometheus.rh-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090

  # Grafana dashboard interface
  - host: grafana.rh-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
