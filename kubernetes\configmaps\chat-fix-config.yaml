apiVersion: v1
kind: ConfigMap
metadata:
  name: chat-fix-config
  namespace: rh-system
data:
  chat_fix.py: |
    # Fix temporaire pour le chat - Ajouter SessionAuthentication
    from django.conf import settings
    
    # Modifier la configuration DRF
    settings.REST_FRAMEWORK['DEFAULT_AUTHENTICATION_CLASSES'] = (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    )
    
    print("✅ Chat fix appliqué - SessionAuthentication ajouté")
