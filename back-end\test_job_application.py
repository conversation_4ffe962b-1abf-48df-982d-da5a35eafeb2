#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test de la fonctionnalité de nouvelle candidature avec Gmail SMTP
"""

import os
import sys
import django
import requests
import json
from io import BytesIO

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')

try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from django.core.files.uploadedfile import SimpleUploadedFile
from Rh_app.models import JobApplication, User
from Rh_app.utils import send_email_notification, get_admin_emails

def test_job_application_emails():
    """Test des emails pour nouvelle candidature"""
    print("\n📋 Test des emails pour nouvelle candidature")
    print("=" * 60)
    
    try:
        # Simuler une nouvelle candidature
        print("1. Simulation d'une nouvelle candidature...")
        
        # Test email au candidat
        candidate_email = "<EMAIL>"
        candidate_subject = "Votre candidature a été reçue"
        candidate_message = """Bonjour Jean Dupont,

Nous avons bien reçu votre candidature pour le poste de Développeur Full Stack.

Vous pouvez suivre l'état de votre candidature en utilisant les informations suivantes :
- ID de candidature : 999
- Email : <EMAIL>

Votre candidature est actuellement en attente d'approbation par notre équipe RH. 
Vous recevrez un email lorsque votre candidature sera approuvée ou rejetée.

Une fois approuvée, vous pourrez vous connecter avec votre email et le mot de passe que vous avez défini.

Cordialement,
L'équipe RH"""

        print("2. Envoi email de confirmation au candidat...")
        result_candidate = send_email_notification(
            subject=candidate_subject,
            message=candidate_message,
            recipient_list=[candidate_email],
            email_type='job_application_received',
            reference_id=999
        )
        
        if result_candidate:
            print("   ✅ Email de confirmation candidat envoyé !")
        else:
            print("   ❌ Échec envoi email candidat")
            return False
        
        # Test email aux administrateurs
        print("3. Envoi email de notification aux administrateurs...")
        admin_emails = get_admin_emails()
        print(f"   Emails admin trouvés: {admin_emails}")
        
        if admin_emails:
            admin_subject = "Nouvelle candidature : Développeur Full Stack"
            admin_message = """Une nouvelle candidature a été soumise :

Poste : Développeur Full Stack
Type : employee
Candidat : Jean Dupont
Email : <EMAIL>
Téléphone : +33 1 23 45 67 89

Connectez-vous au système pour examiner cette candidature."""

            result_admin = send_email_notification(
                subject=admin_subject,
                message=admin_message,
                recipient_list=admin_emails,
                email_type='new_job_application',
                reference_id=999
            )
            
            if result_admin:
                print("   ✅ Email de notification admin envoyé !")
            else:
                print("   ❌ Échec envoi email admin")
                return False
        else:
            print("   ⚠️ Aucun email admin trouvé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test candidature: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_job_application_approval():
    """Test des emails pour approbation de candidature"""
    print("\n✅ Test des emails pour approbation de candidature")
    print("=" * 60)
    
    try:
        # Simuler l'approbation d'une candidature
        print("1. Simulation d'approbation de candidature...")
        
        approval_subject = "Votre candidature a été approuvée !"
        approval_message = """Bonjour Jean Dupont,

Félicitations ! Votre candidature pour le poste de Développeur Full Stack a été approuvée.

Voici vos informations de connexion :
- Nom d'utilisateur : jean.dupont
- Email : <EMAIL>
- Mot de passe : Celui que vous avez défini lors de votre candidature

Vous pouvez maintenant vous connecter au système RH à l'adresse suivante :
http://localhost:5175/login

Bienvenue dans notre équipe !

Cordialement,
L'équipe RH"""

        print("2. Envoi email d'approbation au candidat...")
        result = send_email_notification(
            subject=approval_subject,
            message=approval_message,
            recipient_list=["<EMAIL>"],
            email_type='job_application_approved',
            reference_id=999
        )
        
        if result:
            print("   ✅ Email d'approbation envoyé !")
            return True
        else:
            print("   ❌ Échec envoi email d'approbation")
            return False
        
    except Exception as e:
        print(f"❌ Erreur test approbation: {e}")
        return False

def test_job_application_rejection():
    """Test des emails pour rejet de candidature"""
    print("\n❌ Test des emails pour rejet de candidature")
    print("=" * 60)
    
    try:
        # Simuler le rejet d'une candidature
        print("1. Simulation de rejet de candidature...")
        
        rejection_subject = "Votre candidature a été rejetée"
        rejection_message = """Bonjour Jean Dupont,

Nous regrettons de vous informer que votre candidature pour le poste de Développeur Full Stack a été rejetée.

Nous vous remercions de l'intérêt que vous portez à notre entreprise et vous souhaitons bonne chance dans vos recherches futures.

Conformément à notre politique de confidentialité, toutes vos données personnelles ont été supprimées de notre système.

Cordialement,
L'équipe RH"""

        print("2. Envoi email de rejet au candidat...")
        result = send_email_notification(
            subject=rejection_subject,
            message=rejection_message,
            recipient_list=["<EMAIL>"],
            email_type='job_application_rejected',
            reference_id=999
        )
        
        if result:
            print("   ✅ Email de rejet envoyé !")
            return True
        else:
            print("   ❌ Échec envoi email de rejet")
            return False
        
    except Exception as e:
        print(f"❌ Erreur test rejet: {e}")
        return False

def test_job_application_api():
    """Test de l'API de candidature"""
    print("\n🔗 Test de l'API de candidature")
    print("=" * 60)
    
    try:
        # Données de test pour une candidature
        application_data = {
            "application_type": "employee",
            "position": "Développeur Full Stack - Test Gmail SMTP",
            "first_name": "Jean",
            "last_name": "Dupont",
            "username": "jean.dupont.test",
            "email": "<EMAIL>",
            "phone": "+33 1 23 45 67 89",
            "education": "Master en Informatique",
            "experience": "3 ans d'expérience en développement web",
            "motivation": "Passionné par le développement et désireux de rejoindre votre équipe pour contribuer aux projets innovants.",
            "password": "TestPassword123!",
            "github_profile": "https://github.com/jeandupont"
        }
        
        print("1. Envoi de candidature via API...")
        print(f"   Poste: {application_data['position']}")
        print(f"   Candidat: {application_data['first_name']} {application_data['last_name']}")
        print(f"   Email: {application_data['email']}")
        
        # Créer un fichier CV factice
        cv_content = b"CV de Jean Dupont - Developpeur Full Stack\nExperience: 3 ans\nCompetences: Python, Django, React, etc."
        
        # Préparer les données multipart
        files = {
            'cv_file': ('cv_jean_dupont.txt', cv_content, 'text/plain')
        }
        
        # Envoyer la candidature
        response = requests.post(
            "http://localhost:8000/job-applications/",
            data=application_data,
            files=files,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"   ✅ Candidature créée avec ID: {response_data.get('id')}")
            print("   📧 Emails automatiques envoyés :")
            print("      - Confirmation au candidat")
            print("      - Notification aux administrateurs")
            return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Détails: {error_data}")
            except:
                print(f"   Réponse brute: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test API candidature: {e}")
        return False

def run_all_job_application_tests():
    """Exécuter tous les tests de candidature"""
    print("🚀 TEST COMPLET DES FONCTIONNALITÉS DE CANDIDATURE")
    print("=" * 70)
    
    tests = [
        ("Emails Nouvelle Candidature", test_job_application_emails),
        ("Emails Approbation Candidature", test_job_application_approval),
        ("Emails Rejet Candidature", test_job_application_rejection),
        ("API Candidature Complète", test_job_application_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS CANDIDATURE")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<50} {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS CANDIDATURE RÉUSSIS !")
        print("📧 Vérifiez votre boîte email Gmail")
        print("🚀 Système de candidature entièrement opérationnel avec Gmail SMTP")
    else:
        print("⚠️ Certains tests ont échoué")
        print("🔧 Vérifiez la configuration et les logs")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_job_application_tests()
    sys.exit(0 if success else 1)
