import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  define: {
    global: 'globalThis',
  },
  server: {
    port: 5173,
    host: true,
    allowedHosts: ['localhost', '127.0.0.1', 'rh-system.local']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      events: 'events',
      util: 'util',
      buffer: 'buffer',
    }
  },
  optimizeDeps: {
    include: ['events', 'util', 'buffer'],
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    minify: false, // Désactiver la minification pour le débogage
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom']
        }
      }
    }
  }
});
