#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test de la fonctionnalité de réinitialisation de mot de passe
"""

import requests
import json

def test_forgot_password():
    """Test de l'endpoint forgot-password"""
    
    url = "http://localhost:8000/auth/forgot-password/"
    data = {
        "email": "<EMAIL>"
    }
    
    print("🔍 Test de la réinitialisation de mot de passe")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Email: {data['email']}")
    print()
    
    try:
        print("📧 Envoi de la demande de réinitialisation...")
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Demande de réinitialisation envoyée avec succès !")
            print("📧 Vérifiez votre boîte email Gmail")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Détails: {error_data}")
            except:
                print(f"Réponse brute: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    success = test_forgot_password()
    
    print("=" * 50)
    if success:
        print("✅ Test réussi ! Email de réinitialisation envoyé")
        print("📧 Vérifiez votre boîte email Gmail")
    else:
        print("❌ Test échoué")
        print("🔧 Vérifiez les logs du backend Docker")
