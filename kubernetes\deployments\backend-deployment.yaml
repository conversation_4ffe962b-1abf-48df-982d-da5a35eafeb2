apiVersion: apps/v1
kind: Deployment
metadata:
  name: rh-backend
  namespace: rh-system
  labels:
    app: rh-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rh-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: rh-backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        prometheus.io/scheme: "http"
    spec:
      containers:
      - name: rh-backend
        image: waelbenabid/rh-system-backend:latest
        imagePullPolicy: Always
        command: ["/bin/bash", "/scripts/init.sh"]
        ports:
        - containerPort: 8000
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DEBUG
        - name: ALLOWED_HOSTS
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ALLOWED_HOSTS
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_NAME
        - name: DATABASE_USER
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: DATABASE_PASSWORD
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: SECRET_KEY
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: EMAIL_HOST_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: EMAIL_HOST_PASSWORD
        - name: SENDGRID_API_KEY
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: SENDGRID_API_KEY
        # Variables d'environnement Zipkin
        - name: ZIPKIN_ENABLED
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ZIPKIN_ENABLED
        - name: ZIPKIN_SERVER_URL
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ZIPKIN_SERVER_URL
        - name: ZIPKIN_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ZIPKIN_SERVICE_NAME
        - name: ZIPKIN_SAMPLE_RATE
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ZIPKIN_SAMPLE_RATE
        # Variables d'environnement Email
        - name: EMAIL_BACKEND
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: EMAIL_BACKEND
        - name: DEFAULT_FROM_EMAIL
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DEFAULT_FROM_EMAIL
        volumeMounts:
        - name: init-script
          mountPath: /scripts
        - name: media-storage
          mountPath: /app/media_v2
        - name: static-storage
          mountPath: /app/static
      volumes:
      - name: init-script
        configMap:
          name: backend-init-script
          defaultMode: 0755
      - name: media-storage
        persistentVolumeClaim:
          claimName: media-pvc
      - name: static-storage
        emptyDir: {}
