# Generated by Django 5.2 on 2025-05-16 01:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0019_remove_leave_attachment_remove_leave_attachment_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='leave',
            name='attachment',
            field=models.FileField(blank=True, help_text='Pièce jointe pour la demande de congé', null=True, upload_to='leave_attachments/'),
        ),
        migrations.AddField(
            model_name='leave',
            name='attachment_name',
            field=models.CharField(blank=True, help_text='Nom original du fichier', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='leave',
            name='attachment_type',
            field=models.CharField(blank=True, help_text='Type MIME du fichier attaché', max_length=50, null=True),
        ),
    ]
