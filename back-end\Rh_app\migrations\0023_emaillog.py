# Generated by Django 5.2 on 2025-05-18 13:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0022_meeting_cancellation_reason'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_type', models.CharField(choices=[('late_mission', 'Mission en retard'), ('upcoming_deadline', 'Deadline approche'), ('meeting_cancelled', 'Réunion annulée'), ('meeting_scheduled', 'Réunion planifiée'), ('other', 'Autre')], max_length=50)),
                ('reference_id', models.IntegerField(blank=True, help_text="ID de l'objet concerné (mission, réunion, etc.)", null=True)),
                ('sent_at', models.DateT<PERSON><PERSON>ield(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-sent_at'],
            },
        ),
    ]
