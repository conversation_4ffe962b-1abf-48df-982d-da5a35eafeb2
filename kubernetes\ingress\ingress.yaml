apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    # Taille maximale des requêtes
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
  name: rh-system-ingress
  namespace: rh-system
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - rh-system.local
    - zipkin.rh-system.local
    - prometheus.rh-system.local
    - grafana.rh-system.local
    - kibana.rh-system.local
    secretName: rh-system-tls
  rules:
  - host: rh-system.local
    http:
      paths:
      # Frontend Admin Interface
      - path: /admine
        pathType: Prefix
        backend:
          service:
            name: rh-frontend
            port:
              number: 80

      # Backend Admin Interface
      - path: /admin
        pathType: Prefix
        backend:
          service:
            name: rh-backend
            port:
              number: 8000

      # Backend API endpoints
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: rh-backend
            port:
              number: 8000

      # Backend API endpoints (legacy)
      - path: /(users|leaves|missions|work-hours|internships|job-applications|user-sessions|meetings|notifications|system-activities|token|auth|messages|public)
        pathType: ImplementationSpecific
        backend:
          service:
            name: rh-backend
            port:
              number: 8000

      # Media files
      - path: /media
        pathType: Prefix
        backend:
          service:
            name: rh-backend
            port:
              number: 8000

      # Static files
      - path: /static
        pathType: Prefix
        backend:
          service:
            name: rh-backend
            port:
              number: 8000

      # Frontend assets (IMPORTANT: Must be before the default route)
      - path: /assets
        pathType: Prefix
        backend:
          service:
            name: rh-frontend
            port:
              number: 80

      # Frontend default route
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: rh-frontend
            port:
              number: 80

  # Zipkin monitoring interface
  - host: zipkin.rh-system.local
    http:
      paths:
      - path: /api/v2/spans
        pathType: ImplementationSpecific
        backend:
          service:
            name: zipkin
            port:
              number: 9411
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: zipkin
            port:
              number: 9411

  # Prometheus monitoring interface
  - host: prometheus.rh-system.local
    http:
      paths:
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: prometheus
            port:
              number: 9090

  # Grafana monitoring interface
  - host: grafana.rh-system.local
    http:
      paths:
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: grafana
            port:
              number: 3000

  # Kibana logging interface
  - host: kibana.rh-system.local
    http:
      paths:
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: kibana
            port:
              number: 5601
