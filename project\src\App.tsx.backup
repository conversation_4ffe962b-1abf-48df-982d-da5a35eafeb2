import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@radix-ui/react-toast';

// Pages
import Home from './pages/Home';
import AdminDashboard from './pages/admin/AdminDashboard';
import EmployeeManagement from './pages/admin/EmployeeManagement';
import InternManagementAdmin from './pages/admin/InternManagementAdmin';
import ApplicationsReview from './pages/admin/ApplicationsReview';
import MissionManagement from './pages/admin/MissionManagement';
import PendingApproval from './pages/auth/PendingApproval';
import Login from './pages/auth/Login';
import ApplicationSuccess from './pages/ApplicationSuccess';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './ResetPassword';
import NotFound from './pages/NotFound';
import { useEffect } from 'react';

function AppWithAlerts() {
  const queryClient = new QueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Toaster />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/application-success" element={<ApplicationSuccess />} />
          <Route path="/pending-approval" element={<PendingApproval />} />
          <Route path="/admine" element={<AdminDashboard />} />
          <Route path="/admine/employee-management" element={<EmployeeManagement />} />
          <Route path="/admine/intern-management" element={<InternManagementAdmin />} />
          <Route path="/admine/applications-review" element={<ApplicationsReview />} />
          <Route path="/admine/mission-management" element={<MissionManagement />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </QueryClientProvider>
  );
}

export default AppWithAlerts;
