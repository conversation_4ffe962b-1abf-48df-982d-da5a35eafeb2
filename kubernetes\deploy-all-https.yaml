#!/bin/bash

# Script de déploiement complet avec HTTPS pour tous les services
# Usage: ./deploy-all-https.sh

set -e

echo "🚀 Déploiement complet du système RH avec HTTPS..."

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction pour vérifier si une ressource existe
resource_exists() {
    kubectl get $1 $2 -n $3 &>/dev/null
}

# 1. <PERSON><PERSON>er les namespaces
log_info "Création des namespaces..."
kubectl apply -f kubernetes/namespace/rh-system.yaml
kubectl apply -f kubernetes/monitoring/namespace.yaml
kubectl apply -f kubernetes/logging/namespace.yaml

# 2. Installer cert-manager si nécessaire
log_info "Installation de cert-manager..."
if ! kubectl get namespace cert-manager &>/dev/null; then
    kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
    log_info "Attente du démarrage de cert-manager..."
    kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
fi

# 3. Créer les issuers de certificats
log_info "Configuration des certificats SSL..."
kubectl apply -f kubernetes/cert-manager/selfsigned-issuer.yaml

# 4. Déployer les secrets
log_info "Déploiement des secrets..."
kubectl apply -f kubernetes/secrets/

# 5. Déployer les ConfigMaps
log_info "Déploiement des ConfigMaps..."
kubectl apply -f kubernetes/configmaps/

# 6. Déployer le stockage
log_info "Déploiement du stockage..."
kubectl apply -f kubernetes/storage/

# 7. Déployer PostgreSQL
log_info "Déploiement de PostgreSQL..."
kubectl apply -f kubernetes/deployments/postgres-deployment.yaml
kubectl apply -f kubernetes/services/postgres-service.yaml

# Attendre que PostgreSQL soit prêt
log_info "Attente du démarrage de PostgreSQL..."
kubectl wait --for=condition=ready pod -l app=postgres-db -n rh-system --timeout=300s

# 8. Déployer le backend
log_info "Déploiement du backend..."
kubectl apply -f kubernetes/deployments/backend-deployment.yaml
kubectl apply -f kubernetes/services/backend-service.yaml

# 9. Déployer le frontend
log_info "Déploiement du frontend..."
kubectl apply -f kubernetes/deployments/frontend-deployment.yaml
kubectl apply -f kubernetes/services/frontend-service.yaml

# 10. Déployer les services de monitoring
log_info "Déploiement des services de monitoring..."
kubectl apply -f kubernetes/monitoring/prometheus/
kubectl apply -f kubernetes/monitoring/grafana/
kubectl apply -f kubernetes/monitoring/zipkin/
kubectl apply -f kubernetes/monitoring/postgres-exporter/

# 11. Déployer les services de logging
log_info "Déploiement des services de logging..."
kubectl apply -f kubernetes/logging/elasticsearch/
kubectl apply -f kubernetes/logging/kibana/
kubectl apply -f kubernetes/logging/filebeat/

# 12. Déployer les ingress avec HTTPS
log_info "Déploiement des ingress avec HTTPS..."
kubectl apply -f kubernetes/ingress/

# 13. Attendre que tous les pods soient prêts
log_info "Attente du démarrage de tous les services..."

# Attendre les pods principaux
kubectl wait --for=condition=ready pod -l app=rh-backend -n rh-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=rh-frontend -n rh-system --timeout=300s

# Attendre les pods de monitoring
kubectl wait --for=condition=ready pod -l app=prometheus -n monitoring --timeout=300s || log_warning "Prometheus pas encore prêt"
kubectl wait --for=condition=ready pod -l app=grafana -n monitoring --timeout=300s || log_warning "Grafana pas encore prêt"
kubectl wait --for=condition=ready pod -l app=zipkin -n monitoring --timeout=300s || log_warning "Zipkin pas encore prêt"

# Attendre les pods de logging
kubectl wait --for=condition=ready pod -l app=kibana -n logging --timeout=300s || log_warning "Kibana pas encore prêt"

log_success "Déploiement terminé !"

echo ""
echo "🌐 URLs d'accès avec HTTPS :"
echo "  • Application principale: https://rh-system.local"
echo "  • Grafana (monitoring):   https://grafana.rh-system.local"
echo "  • Prometheus (métriques): https://prometheus.rh-system.local"
echo "  • Zipkin (tracing):       https://zipkin.rh-system.local"
echo "  • Kibana (logs):          https://kibana.rh-system.local"
echo ""
echo "📝 Ajoutez ces entrées à votre fichier /etc/hosts (ou C:\\Windows\\System32\\drivers\\etc\\hosts) :"
echo "127.0.0.1 rh-system.local"
echo "127.0.0.1 grafana.rh-system.local"
echo "127.0.0.1 prometheus.rh-system.local"
echo "127.0.0.1 zipkin.rh-system.local"
echo "127.0.0.1 kibana.rh-system.local"
echo ""
echo "🔐 Identifiants par défaut :"
echo "  • Grafana: admin / admin"
echo "  • Application: wael / Abidos\$123"
