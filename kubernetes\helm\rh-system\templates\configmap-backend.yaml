apiVersion: v1
kind: ConfigMap
metadata:
  name: rh-backend-config
  namespace: rh-system
data:
  DEBUG: "{{ .Values.backend.config.debug }}"
  ALLOWED_HOSTS: "{{ .Values.backend.config.allowedHosts }}"
  DATABASE_NAME: "{{ .Values.backend.database.name }}"
  DATABASE_USER: "{{ .Values.backend.database.user }}"
  DATABASE_HOST: "{{ .Values.backend.database.host }}"
  DATABASE_PORT: "{{ .Values.backend.database.port }}"
  EMAIL_BACKEND: "{{ .Values.backend.config.emailBackend }}"
  FRONTEND_URL: "{{ .Values.backend.config.frontendUrl }}"
