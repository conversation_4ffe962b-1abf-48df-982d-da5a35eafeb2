{{- if .Values.backend.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.backend.name }}
  namespace: rh-system
  labels:
    app: {{ .Values.backend.name }}
spec:
  replicas: {{ .Values.backend.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.backend.name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{ .Values.backend.name }}
    spec:
      containers:
      - name: {{ .Values.backend.name }}
        image: "{{ .Values.backend.image.repository }}:{{ .Values.backend.image.tag }}"
        imagePullPolicy: {{ .Values.backend.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.backend.service.port }}
          name: http
        resources:
          {{- toYaml .Values.backend.resources | nindent 10 }}
        env:
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DEBUG
        - name: ALLOWED_HOSTS
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: ALLOWED_HOSTS
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_NAME
        - name: DATABASE_USER
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: DATABASE_PASSWORD
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: rh-backend-config
              key: DATABASE_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: SECRET_KEY
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: EMAIL_HOST_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: EMAIL_HOST_PASSWORD
        - name: SENDGRID_API_KEY
          valueFrom:
            secretKeyRef:
              name: rh-backend-secrets
              key: SENDGRID_API_KEY
        readinessProbe:
          httpGet:
            path: /health/
            port: {{ .Values.backend.service.port }}
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health/
            port: {{ .Values.backend.service.port }}
          initialDelaySeconds: 30
          periodSeconds: 20
      restartPolicy: Always
{{- end }}
