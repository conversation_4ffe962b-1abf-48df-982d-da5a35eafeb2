apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: rh-system
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:9.5.2
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: GF_SECURITY_ADMIN_USER
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-user
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-password
        - name: GF_SERVER_ROOT_URL
          value: "%(protocol)s://%(domain)s/"
        - name: GF_SERVER_SERVE_FROM_SUB_PATH
          value: "false"
        - name: GF_INSTALL_PLUGINS
          value: "grafana-clock-panel,grafana-simple-json-datasource"
        # Configuration SMTP pour SendGrid
        - name: GF_SMTP_ENABLED
          value: "true"
        - name: GF_SMTP_HOST
          value: "smtp.sendgrid.net:587"
        - name: GF_SMTP_USER
          value: "apikey"
        - name: GF_SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: sendgrid-api-key
        - name: GF_SMTP_FROM_ADDRESS
          value: "<EMAIL>"
        - name: GF_SMTP_FROM_NAME
          value: "RH System - Grafana"
        - name: GF_SMTP_SKIP_VERIFY
          value: "false"
        - name: GF_SMTP_EHLO_IDENTITY
          value: "rh-system.local"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-provision
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
        - name: grafana-smtp-config
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-provision
        configMap:
          name: grafana-dashboards-provision
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards
      - name: grafana-smtp-config
        configMap:
          name: grafana-smtp-config
