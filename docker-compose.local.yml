# Docker Compose pour le développement local
# Utilise les sources locales au lieu des images Docker Hub
version: '3.8'

services:
  # Base de données PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: rh-postgres-local
    environment:
      POSTGRES_DB: rh_v2
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 12345
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_local:/var/lib/postgresql/data
      - ./back-end/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - app-network-local
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d rh_v2"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Django (développement local)
  backend:
    build:
      context: ./back-end
      dockerfile: Dockerfile.local
    container_name: rh-backend-local
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - ADMIN_USERNAME=wael
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=Abidos$$123
      - ADMIN_FIRSTNAME=Wael
      - ADMIN_LASTNAME=Ben Abid
      - DATABASE_NAME=rh_v2
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=12345
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
      - ZIPKIN_ENABLED=True
      - ZIPKIN_SERVER_URL=http://zipkin:9411
      - ZIPKIN_SERVICE_NAME=rh-backend-local
      - ZIPKIN_SAMPLE_RATE=100.0
      - SENDGRID_API_KEY=${SENDGRID_API_KEY:-*********************************************************************}
      - DEFAULT_FROM_EMAIL=RH System <<EMAIL>>
      - FRONTEND_URL=http://localhost:5173
    volumes:
      - ./back-end:/app
      - backend_media_local:/app/media_v2
      - backend_static_local:/app/static
    networks:
      - app-network-local
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      zipkin:
        condition: service_started
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python manage.py migrate &&
        python manage.py shell -c \"
        from django.contrib.auth import get_user_model;
        User = get_user_model();
        if not User.objects.filter(username='wael').exists():
            User.objects.create_superuser('wael', '<EMAIL>', 'Abidos$$123', first_name='Wael', last_name='Ben Abid', user_type='admin');
            print('Superuser created.')
        else:
            print('Superuser already exists.')
        \" &&
        python manage.py collectstatic --noinput &&
        python manage.py runserver 0.0.0.0:8000
      "

  # Frontend React (développement local)
  frontend:
    build:
      context: ./project
      dockerfile: Dockerfile.local
    container_name: rh-frontend-local
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_ZIPKIN_URL=http://localhost:9411
      - VITE_ZIPKIN_ENABLED=true
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./project:/app
      - /app/node_modules
    networks:
      - app-network-local
    restart: unless-stopped
    depends_on:
      - backend
      - zipkin
    command: npm run dev -- --host 0.0.0.0 --port 5173

  # Zipkin pour le tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: rh-zipkin-local
    ports:
      - "9411:9411"
    networks:
      - app-network-local
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:9411/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis pour le cache (optionnel)
  redis:
    image: redis:7-alpine
    container_name: rh-redis-local
    ports:
      - "6379:6379"
    networks:
      - app-network-local
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  postgres_data_local:
    driver: local
  backend_media_local:
    driver: local
  backend_static_local:
    driver: local

networks:
  app-network-local:
    driver: bridge
    name: rh-local-network
