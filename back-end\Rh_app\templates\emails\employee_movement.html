<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mouvement d'employé</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .content { background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .button { display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>
                {% if movement_type == 'arrival' %}
                    👋 Nouvelle arrivée
                {% elif movement_type == 'departure' %}
                    👋 Départ d'employé
                {% elif movement_type == 'assignment' %}
                    📋 Nouvelle assignation
                {% else %}
                    📢 Mouvement d'employé
                {% endif %}
            </h2>
        </div>
        
        <div class="content">
            <p>Bonjour,</p>
            
            <div class="info">
                <strong>ℹ️ Information :</strong> 
                {% if movement_type == 'arrival' %}
                    Un nouvel employé a rejoint l'équipe.
                {% elif movement_type == 'departure' %}
                    Un employé a quitté l'équipe.
                {% elif movement_type == 'assignment' %}
                    Un stagiaire a été assigné à un superviseur.
                {% else %}
                    Mouvement d'employé détecté.
                {% endif %}
            </div>
            
            <h3>Détails de l'employé :</h3>
            <ul>
                <li><strong>Nom complet :</strong> {{ user.get_full_name }}</li>
                <li><strong>Email :</strong> {{ user.email }}</li>
                <li><strong>Rôle :</strong> {{ user.get_role_display }}</li>
                {% if user.department %}
                <li><strong>Département :</strong> {{ user.department }}</li>
                {% endif %}
                {% if user.position %}
                <li><strong>Poste :</strong> {{ user.position }}</li>
                {% endif %}
                <li><strong>Date :</strong> {{ user.date_joined|date:"d/m/Y à H:i" }}</li>
            </ul>
            
            {% if details %}
            <h3>Détails supplémentaires :</h3>
            <p>{{ details }}</p>
            {% endif %}
            
            {% if movement_type == 'assignment' and user.superviseur %}
            <h3>Superviseur assigné :</h3>
            <ul>
                <li><strong>Nom :</strong> {{ user.superviseur.get_full_name }}</li>
                <li><strong>Email :</strong> {{ user.superviseur.email }}</li>
            </ul>
            {% endif %}
            
            <a href="{{ frontend_url }}/users/{{ user.id }}" class="button">Voir le profil</a>
        </div>
        
        <div class="footer">
            <p>Ceci est un email automatique du système RH. Ne pas répondre à cet email.</p>
            <p>Vous recevez cet email car vous êtes administrateur ou superviseur.</p>
        </div>
    </div>
</body>
</html>
