apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: rh-system
data:
  default.conf: |
    server {
        listen 80;
        server_name localhost;

        # Configuration des types MIME
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        # Ajout des types MIME pour les modules JavaScript
        types_hash_max_size 2048;
        types_hash_bucket_size 64;

        # Types MIME personnalisés
        types {
            application/javascript js;
            application/javascript mjs;
            text/css css;
            image/png png;
            image/jpeg jpg jpeg;
            image/svg+xml svg;
            application/font-woff woff;
            application/font-woff2 woff2;
        }

        # Servir le favicon depuis n'importe quel fichier logo2*.png
        location = /favicon.ico {
            try_files /assets/logo2*.png /assets/logo2-*.png =404;
            add_header Content-Type image/png;
            expires 30d;
            access_log off;
            log_not_found off;
        }

        # Configuration pour les fichiers JavaScript (priorité haute)
        location ~* \.(js|mjs)$ {
            root /usr/share/nginx/html;
            add_header Content-Type application/javascript;
            add_header X-Content-Type-Options nosniff;
            expires 30d;
            access_log off;
        }

        # Configuration pour les fichiers assets
        location /assets/ {
            root /usr/share/nginx/html;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
            try_files $uri =404;
        }

        # Route directe pour l'interface d'administration Django
        # Toutes les requêtes vers /admin/ sont envoyées au backend Django
        location /admin/ {
            proxy_pass http://rh-backend:8000/admin/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # Ajouter des en-têtes pour éviter les problèmes de CSRF
            proxy_set_header X-CSRFToken $http_x_csrf_token;
            proxy_set_header Referer $http_referer;

            # Augmenter les timeouts pour les opérations longues
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }



        # Route pour tous les fichiers statiques
        location /static/ {
            proxy_pass http://rh-backend:8000/static/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Désactiver la mise en cache pour le débogage
            expires off;
            add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        }

        # Redirection pour /admin sans slash
        location = /admin {
            return 301 /admin/;
        }

        # Route pour l'interface d'administration frontend
        # Toutes les requêtes vers /admine/ sont gérées par le frontend React
        location /admine/ {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;

            # Ajouter des en-têtes pour éviter les problèmes de cache
            add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
            expires off;
        }

        # Redirection pour /admine sans slash
        location = /admine {
            return 301 /admine/;
        }

        # Route pour l'API
        location ~ ^/(users|leaves|missions|work-hours|internships|job-applications|user-sessions|meetings|notifications|system-activities|token|auth|messages|public)/ {
            # Proxy direct vers le backend
            proxy_pass http://rh-backend:8000$request_uri;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # Ajouter des en-têtes pour éviter les problèmes CORS
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

            # Gérer les requêtes OPTIONS pour le preflight CORS
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        # Route pour les médias
        location /media/ {
            proxy_pass http://rh-backend:8000/media/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Ajouter des en-têtes pour le cache et la sécurité
            add_header Cache-Control "public, max-age=86400";
            add_header X-Content-Type-Options "nosniff";

            # Corriger le problème de contenu mixte
            add_header Content-Security-Policy "upgrade-insecure-requests";
        }

        # Route pour les métriques Prometheus du frontend
        location = /frontend-metrics {
            alias /usr/share/nginx/html/metrics;
            default_type text/plain;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Route pour les métriques Prometheus du backend
        location = /metrics {
            proxy_pass http://rh-backend:8000/metrics;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Route par défaut pour le frontend
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;

            # Pour les fichiers statiques, les servir directement
            location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 30d;
                add_header Cache-Control "public, max-age=2592000";
                try_files $uri =404;
            }

            # Pour tout le reste, rediriger vers index.html (SPA)
            try_files $uri $uri/ /index.html;

            # CSP plus permissif
            add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https: http:; font-src 'self' data: https: http:; style-src 'self' 'unsafe-inline' https: http:;";
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
