"""
Django settings for SystemeRH project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
import re
from dotenv import load_dotenv
from datetime import timedelta

# Charger les variables d'environnement depuis le fichier .env.local s'il existe, sinon depuis .env
env_local_path = Path(__file__).resolve().parent.parent / '.env.local'
if env_local_path.exists():
    load_dotenv(dotenv_path=env_local_path)
else:
    load_dotenv()
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY') or 'django-insecure-' + ''.join(['x' for _ in range(50)])

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True  # Activé pour servir les fichiers statiques

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost').split(',')

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'Rh_app',
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'django_crontab',
    'django_prometheus',
]

MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',  # Doit être en premier
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',  # Réactivé pour la sécurité
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'Rh_app.middleware.CheckLateMissionsMiddleware',  # Middleware pour vérifier les missions en retard
    'Rh_app.zipkin_middleware.ZipkinMiddleware',  # Middleware pour le traçage Zipkin
    'Rh_app.simple_prometheus_middleware.SimplePrometheusMiddleware',  # Middleware pour les métriques personnalisées
    'django_prometheus.middleware.PrometheusAfterMiddleware',  # Doit être en dernier
]

IGNORABLE_404_URLS = [
    re.compile(r'^/favicon\.ico$'),
]

ROOT_URLCONF = 'SystemeRH_v2.urls'
AUTH_USER_MODEL = 'Rh_app.User'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'SystemeRH_v2.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DATABASE_NAME', 'rh_v2'),
        'USER': os.getenv('DATABASE_USER', 'postgres'),
        'PASSWORD': os.getenv('DATABASE_PASSWORD', ''),
        'HOST': os.getenv('DATABASE_HOST', 'localhost'),
        'PORT': os.getenv('DATABASE_PORT', '5433'),
    }
}

# Détection automatique de l'environnement Docker
# Si nous sommes dans Docker, la variable DATABASE_HOST sera 'db'
if os.getenv('DATABASE_HOST') == 'db':
    DATABASES['default']['HOST'] = 'db'
    DATABASES['default']['PORT'] = '5432'


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Assurez-vous que Django peut trouver les fichiers statiques de l'admin
STATICFILES_DIRS = []

# Configuration pour servir les fichiers statiques en production
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Servir les fichiers statiques même en mode production
SERVE_STATIC_FILES = True


# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media_v2')

CORS_ALLOW_ALL_ORIGINS = True  # Temporairement pour le débogage
CORS_ALLOWED_ORIGINS = os.getenv('CORS_ALLOWED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:80,http://localhost,http://127.0.0.1:80,http://127.0.0.1,http://frontend,http://frontend:80,https://rh-system.local,http://rh-system.local').split(',')

CORS_ALLOW_CREDENTIALS = True
CSRF_TRUSTED_ORIGINS = os.getenv('CSRF_TRUSTED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:80,http://localhost,http://127.0.0.1:80,http://127.0.0.1,http://frontend,http://frontend:80,https://rh-system.local,http://rh-system.local').split(',')

# Paramètres de sécurité pour HTTPS
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SESSION_COOKIE_SECURE = True  # Activé pour la sécurité
CSRF_COOKIE_SECURE = True  # Activé pour la sécurité
CSRF_COOKIE_HTTPONLY = False  # Permettre l'accès via JavaScript
SESSION_COOKIE_HTTPONLY = True  # Empêcher l'accès via JavaScript
CSRF_TRUSTED_ORIGINS = ['https://rh-system.local', 'http://rh-system.local']

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
        'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),  # Augmenter à 30 jours
    'REFRESH_TOKEN_LIFETIME': timedelta(days=90),  # Augmenter à 90 jours
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'AUTH_HEADER_TYPES': ('Bearer',),
}

# Email Configuration - Support Gmail SMTP et SendGrid
EMAIL_BACKEND = os.getenv('EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')
EMAIL_PROVIDER = os.getenv('EMAIL_PROVIDER', 'gmail')  # 'gmail' ou 'sendgrid'
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', 'RH System <<EMAIL>>')

# Configuration SMTP
if EMAIL_BACKEND == 'django.core.mail.backends.smtp.EmailBackend':
    if EMAIL_PROVIDER == 'gmail':
        # Configuration Gmail SMTP
        EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
        EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
        EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'True').lower() in ('true', '1', 't')
        EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')  # Votre email Gmail
        EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')  # Mot de passe d'application
    elif EMAIL_PROVIDER == 'sendgrid':
        # Configuration SendGrid SMTP (OBSOLÈTE - Utiliser Gmail SMTP)
        # Cette configuration est conservée pour compatibilité mais non recommandée
        EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.sendgrid.net')
        EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
        EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'True').lower() in ('true', '1', 't')
        EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', 'apikey')
        EMAIL_HOST_PASSWORD = os.getenv('SENDGRID_API_KEY', '')
    else:
        # Fallback vers console backend
        EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
        EMAIL_CONSOLE_STDOUT = True
        EMAIL_CONSOLE_STDERR = False
else:
    # Console backend pour développement
    EMAIL_CONSOLE_STDOUT = True
    EMAIL_CONSOLE_STDERR = False
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:5173')

# Paramètres supplémentaires pour améliorer la délivrabilité des emails
SERVER_EMAIL = DEFAULT_FROM_EMAIL
ADMINS = [('Admin', '<EMAIL>')]
MANAGERS = ADMINS

# Configuration Zipkin
# Activer Zipkin par défaut
ZIPKIN_ENABLED = os.getenv('ZIPKIN_ENABLED', 'True').lower() in ('true', '1', 't')
# Utiliser l'URL du service Zipkin local
ZIPKIN_SERVER_URL = os.getenv('ZIPKIN_SERVER_URL', 'http://localhost:9411')
ZIPKIN_SERVICE_NAME = os.getenv('ZIPKIN_SERVICE_NAME', 'rh-backend')
# Augmenter le taux d'échantillonnage pour capturer toutes les traces
ZIPKIN_SAMPLE_RATE = float(os.getenv('ZIPKIN_SAMPLE_RATE', '100.0'))

# Configuration des en-têtes d'email
EMAIL_SUBJECT_PREFIX = '[RH System] '
EMAIL_USE_LOCALTIME = True

# Configuration de limitation des emails
EMAIL_DAILY_LIMIT = int(os.getenv('EMAIL_DAILY_LIMIT', '100'))
EMAIL_HOURLY_LIMIT = int(os.getenv('EMAIL_HOURLY_LIMIT', '20'))
EMAIL_PER_USER_DAILY_LIMIT = int(os.getenv('EMAIL_PER_USER_DAILY_LIMIT', '10'))

# URL du frontend pour les liens dans les emails
FRONTEND_URL = os.getenv('FRONTEND_URL', 'https://rh-system.local')

# Configuration Zipkin unifiée
def get_zipkin_url():
    """Déterminer l'URL Zipkin selon l'environnement"""
    # Vérifier si nous sommes dans un conteneur Docker
    import socket
    try:
        # Essayer de résoudre le nom du service Docker
        socket.gethostbyname('zipkin')
        # Si ça marche, nous sommes dans Docker
        return 'http://zipkin:9411'
    except socket.gaierror:
        # Pas dans Docker, vérifier l'environnement
        if any('rh-system.local' in host for host in ALLOWED_HOSTS):
            # Environnement Kubernetes
            return 'https://zipkin.rh-system.local'
        else:
            # Environnement local
            return 'http://localhost:9411'

ZIPKIN_URL = os.getenv('ZIPKIN_URL', get_zipkin_url())
ZIPKIN_SERVICE_NAME = 'rh-backend'

# Configuration de journalisation
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'Rh_app': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# Configuration des tâches cron
CRONJOBS = [
    # Vérifier les missions en retard tous les jours à minuit
    ('0 0 * * *', 'Rh_app.management.commands.check_late_missions.Command.handle'),
    # Vérifier les missions dont la date limite approche tous les jours à 8h du matin
    ('0 8 * * *', 'Rh_app.management.commands.check_upcoming_deadlines.Command.handle'),
]