apiVersion: v1
kind: Pod
metadata:
  name: postgres-backup-management
  namespace: rh-system
spec:
  containers:
  - name: postgres-backup-management
    image: busybox:1.36
    command:
    - /bin/sh
    - -c
    - "echo 'Backup management pod is running' && touch /backup/pod-ready && while true; do sleep 3600; done"
    volumeMounts:
    - name: backup-volume
      mountPath: /backup
    - name: backup-scripts
      mountPath: /scripts
    env:
    - name: POSTGRES_PASSWORD
      value: "postgres"
    - name: AZURE_STORAGE_KEY
      value: "default-key-for-development-only"
    resources:
      requests:
        memory: "64Mi"
        cpu: "100m"
      limits:
        memory: "128Mi"
        cpu: "200m"
  volumes:
  - name: backup-volume
    persistentVolumeClaim:
      claimName: postgres-backup-pvc
  - name: backup-scripts
    configMap:
      name: postgres-backup-scripts
      defaultMode: 0755
