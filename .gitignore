# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
media_v2/
staticfiles_v2/
static/
# Les scripts d'initialisation contiennent des informations personnelles
back-end/entrypoint.sh
back-end/docker-entrypoint.sh

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
back-end/.env
project/.env
*/settings_local.py
*/.env.*
!*.env.template
!*.env.example
*/secrets.py
# Fichiers spécifiques contenant des informations sensibles
back-end/env.py

# Tous les fichiers de secrets Kubernetes
kubernetes/secrets/*.yaml
!kubernetes/secrets/*-template.yaml
kubernetes/monitoring/exporters/*-secret.yaml
!kubernetes/monitoring/exporters/*-secret-template.yaml
kubernetes/monitoring/grafana/*-secret.yaml
!kubernetes/monitoring/grafana/*-secret-template.yaml
kubernetes/logging/elasticsearch/*-secret.yaml
!kubernetes/logging/elasticsearch/*-secret-template.yaml
kubernetes/backup/*-secrets.yaml
!kubernetes/backup/*-secrets-template.yaml

# Certificats SSL
certs/
*.key
*.crt
*.pem
*.pfx
*.p12

# Fichiers de sauvegarde de base de données
db_backups/

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# React
/build
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo

# Docker
.docker/
docker-compose.override.yml

# Misc
.coverage
htmlcov/
.pytest_cache/

# Images et médias
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.webp
back-end/media_v2/profile_images/
back-end/project/src/assets/images/
# Exception pour les templates et les fichiers de configuration
!*.svg.template
!*.png.template
# Exception pour les logos
!project/src/assets/images/logo.png
!project/src/assets/images/logo2.png
