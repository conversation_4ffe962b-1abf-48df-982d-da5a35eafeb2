#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test Gmail SMTP dans Docker
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')

try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from django.core.mail import send_mail, EmailMessage
from django.conf import settings

def test_gmail_smtp_docker():
    """Test Gmail SMTP dans l'environnement Docker"""
    
    print("🐳 Test Gmail SMTP dans Docker")
    print("=" * 50)
    
    print("🔧 Configuration Django Email:")
    print(f"   EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"   EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', 'Non configuré')}")
    print(f"   EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'Non configuré')}")
    print(f"   EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Non configuré')}")
    print(f"   EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', 'Non configuré')}")
    print(f"   DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()
    
    # Vérifier les variables d'environnement
    print("🔍 Variables d'environnement:")
    env_vars = ['EMAIL_BACKEND', 'EMAIL_PROVIDER', 'EMAIL_HOST', 'EMAIL_PORT', 
                'EMAIL_USE_TLS', 'EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD']
    for var in env_vars:
        value = os.getenv(var, 'Non défini')
        if 'PASSWORD' in var:
            value = '***' if value != 'Non défini' else 'Non défini'
        print(f"   {var}: {value}")
    print()

    print("📧 Test envoi email Gmail SMTP...")
    
    try:
        # Test 1: Email simple
        print("1. Test email simple...")
        result = send_mail(
            subject='✅ Test RH System - Docker + Gmail SMTP',
            message='''Félicitations !

Docker avec Gmail SMTP fonctionne parfaitement !

Configuration testée :
- Conteneur Docker backend
- Variables d'environnement Gmail
- Authentification SMTP réussie
- Envoi d'email fonctionnel

Votre système RH est prêt pour la production !''',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
        
        print(f"   ✅ Email simple envoyé ! Résultat: {result}")
        
        # Test 2: Email HTML
        print("2. Test email HTML...")
        html_content = """
        <html>
        <body>
            <h2>🐳 Test RH System - Docker + Gmail SMTP</h2>
            <p><strong>Félicitations !</strong> Docker avec Gmail SMTP fonctionne parfaitement.</p>
            
            <h3>✅ Configuration validée :</h3>
            <ul>
                <li>Conteneur Docker backend opérationnel</li>
                <li>Variables d'environnement Gmail configurées</li>
                <li>Connexion SMTP Gmail établie</li>
                <li>Authentification réussie</li>
                <li>Envoi d'emails fonctionnel</li>
            </ul>
            
            <h3>🚀 Prêt pour la production :</h3>
            <ul>
                <li>Notifications utilisateurs</li>
                <li>Réinitialisations de mot de passe</li>
                <li>Alertes administrateur</li>
                <li>Rapports automatiques</li>
            </ul>
            
            <p><em>Votre système RH Docker est opérationnel !</em></p>
            
            <hr>
            <small>Email envoyé depuis le conteneur Docker backend</small>
        </body>
        </html>
        """
        
        email = EmailMessage(
            subject='🐳 Test RH System - Docker + Gmail SMTP (HTML)',
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=['<EMAIL>'],
        )
        email.content_subtype = "html"
        email.send()
        
        print("   ✅ Email HTML envoyé !")
        print()
        print("🎉 TOUS LES TESTS DOCKER RÉUSSIS !")
        print("📧 Vérifiez votre boîte email Gmail")
        print("🐳 Docker + Gmail SMTP parfaitement configuré !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gmail_smtp_docker()
    
    print("=" * 50)
    if success:
        print("✅ Configuration Docker + Gmail SMTP validée !")
        print("🎯 Prêt pour la production Docker")
    else:
        print("❌ Problème de configuration détecté")
        print("🔧 Vérifiez les logs Docker et la connectivité réseau")
