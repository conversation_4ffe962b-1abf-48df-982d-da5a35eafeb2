apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-smtp-config
  namespace: rh-system
data:
  grafana.ini: |
    [smtp]
    enabled = true
    host = smtp.sendgrid.net:587
    user = apikey
    from_address = <EMAIL>
    from_name = RH System - Grafana
    skip_verify = false
    ehlo_identity = rh-system.local

    [emails]
    welcome_email_on_sign_up = false
    templates_pattern = emails/*.html

    [server]
    root_url = %(protocol)s://%(domain)s/
    serve_from_sub_path = false

    [security]
    admin_user = admin
    admin_password = admin
    disable_gravatar = false

    [users]
    allow_sign_up = false
    allow_org_create = false
    auto_assign_org = true
    auto_assign_org_role = Viewer

    [auth.anonymous]
    enabled = false

    [log]
    mode = console
    level = info
