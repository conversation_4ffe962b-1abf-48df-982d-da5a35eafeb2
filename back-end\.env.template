# Ce fichier est un modèle pour votre fichier .env
# Copiez ce fichier en .env et modifiez les valeurs selon votre environnement

SECRET_KEY=django-insecure-VOTRE_CLE_SECRETE_ICI
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,backend,db
DATABASE_NAME=rh_v2
DATABASE_USER=postgres
DATABASE_PASSWORD=VOTRE_MOT_DE_PASSE_ICI
DATABASE_HOST=db
DATABASE_PORT=5432

# Configuration SendGrid
SENDGRID_API_KEY="VOTRE_CLE_API_SENDGRID_ICI"
DEFAULT_FROM_EMAIL="RH System <<EMAIL>>"

# Configuration Email
EMAIL_BACKEND="django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST_USER="<EMAIL>"
EMAIL_HOST_PASSWORD="votre_mot_de_passe_application_email"

# Limitation des emails
EMAIL_DAILY_LIMIT="100"
EMAIL_HOURLY_LIMIT="20"
EMAIL_PER_USER_DAILY_LIMIT="10"
