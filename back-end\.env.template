# Ce fichier est un modèle pour votre fichier .env
# Copiez ce fichier en .env et modifiez les valeurs selon votre environnement

SECRET_KEY=django-insecure-VOTRE_CLE_SECRETE_ICI
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,backend,db
DATABASE_NAME=rh_v2
DATABASE_USER=postgres
DATABASE_PASSWORD=VOTRE_MOT_DE_PASSE_ICI
DATABASE_HOST=db
DATABASE_PORT=5432

# Configuration Gmail SMTP
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_PROVIDER=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre_mot_de_passe_application_gmail
DEFAULT_FROM_EMAIL=RH System <<EMAIL>>

# Note: Pour obtenir un mot de passe d'application Gmail:
# 1. Activez la vérification en 2 étapes sur votre compte Gmail
# 2. Allez dans Paramètres > Sécurité > Mots de passe d'application
# 3. Générez un mot de passe d'application pour "Autre (nom personnalisé)"
# 4. Utilisez ce mot de passe de 16 caractères dans EMAIL_HOST_PASSWORD

# Limitation des emails
EMAIL_DAILY_LIMIT="100"
EMAIL_HOURLY_LIMIT="20"
EMAIL_PER_USER_DAILY_LIMIT="10"
