from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
import os

User = get_user_model()

class Command(BaseCommand):
    help = 'Crée automatiquement un compte admin par défaut'

    def handle(self, *args, **options):
        # Récupérer les informations admin depuis les variables d'environnement
        admin_username = os.getenv('ADMIN_USERNAME', 'wael')
        admin_email = os.getenv('ADMIN_EMAIL', '<EMAIL>')
        admin_password = os.getenv('ADMIN_PASSWORD', 'Abidos$123')
        admin_firstname = os.getenv('ADMIN_FIRSTNAME', 'Wael')
        admin_lastname = os.getenv('ADMIN_LASTNAME', 'Ben Abid')

        self.stdout.write(f'🔍 Vérification du compte admin: {admin_username}')

        # Vérifier si l'admin existe déjà
        if User.objects.filter(username=admin_username).exists():
            admin_user = User.objects.get(username=admin_username)
            self.stdout.write(f'✅ Admin existe déjà: {admin_user.username} ({admin_user.user_type})')

            # Mettre à jour vers admin si nécessaire
            updated = False
            if admin_user.user_type != 'admin':
                admin_user.user_type = 'admin'
                updated = True
            if not admin_user.is_staff:
                admin_user.is_staff = True
                updated = True
            if not admin_user.is_superuser:
                admin_user.is_superuser = True
                updated = True
            if not admin_user.is_active:
                admin_user.is_active = True
                updated = True

            # Mettre à jour le mot de passe
            admin_user.set_password(admin_password)
            admin_user.save()

            if updated:
                self.stdout.write(f'🔧 Utilisateur mis à jour vers admin: {admin_user.username}')

            self.stdout.write(f'🔑 Mot de passe mis à jour pour: {admin_user.username}')
            return

        # Créer le compte admin
        try:
            admin_user = User.objects.create_superuser(
                username=admin_username,
                email=admin_email,
                password=admin_password,
                first_name=admin_firstname,
                last_name=admin_lastname,
                user_type='admin'
            )
            
            # S'assurer que tous les flags sont corrects
            admin_user.is_staff = True
            admin_user.is_superuser = True
            admin_user.is_approved = True
            admin_user.is_active = True
            admin_user.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'🎉 Compte admin créé avec succès!\n'
                    f'   Username: {admin_username}\n'
                    f'   Email: {admin_email}\n'
                    f'   Type: {admin_user.user_type}\n'
                    f'   Staff: {admin_user.is_staff}\n'
                    f'   Superuser: {admin_user.is_superuser}\n'
                    f'   Approuvé: {admin_user.is_approved}'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erreur lors de la création de l\'admin: {e}')
            )
