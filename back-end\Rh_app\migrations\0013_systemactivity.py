# Generated by Django 5.2 on 2025-05-09 16:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0012_jobapplication_github_profile_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('login', 'User Login'), ('logout', 'User Logout'), ('create', 'Resource Created'), ('update', 'Resource Updated'), ('delete', 'Resource Deleted'), ('approve', 'Application Approved'), ('reject', 'Application Rejected'), ('complete', 'Mission Completed'), ('assign', 'Mission Assigned'), ('system', 'System Action')], max_length=20)),
                ('action', models.Char<PERSON>ield(max_length=255)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('details', models.TextField(blank=True)),
                ('resource_type', models.CharField(blank=True, max_length=50)),
                ('resource_id', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Activity',
                'verbose_name_plural': 'System Activities',
                'ordering': ['-created_at'],
            },
        ),
    ]
