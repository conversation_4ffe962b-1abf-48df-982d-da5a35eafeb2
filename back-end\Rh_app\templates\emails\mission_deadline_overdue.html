<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mission en retard</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .content { background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .danger { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .button { display: inline-block; padding: 10px 20px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🔴 Mission en retard</h2>
        </div>
        
        <div class="content">
            <p>Bonjour,</p>
            
            <div class="danger">
                <strong>🚨 URGENT :</strong> La mission suivante est en retard !
            </div>
            
            <h3>Détails de la mission :</h3>
            <ul>
                <li><strong>Titre :</strong> {{ mission.titre }}</li>
                <li><strong>Description :</strong> {{ mission.description }}</li>
                <li><strong>Date de fin :</strong> {{ mission.date_fin|date:"d/m/Y" }}</li>
                <li><strong>Jours de retard :</strong> {{ days_remaining|add:"-1"|floatformat:"0" }} jour(s)</li>
                <li><strong>Statut :</strong> {{ mission.get_statut_display }}</li>
                {% if mission.employe %}
                <li><strong>Employé assigné :</strong> {{ mission.employe.get_full_name }}</li>
                {% endif %}
                {% if mission.stagiaire %}
                <li><strong>Stagiaire assigné :</strong> {{ mission.stagiaire.get_full_name }}</li>
                {% endif %}
                {% if mission.superviseur %}
                <li><strong>Superviseur :</strong> {{ mission.superviseur.get_full_name }}</li>
                {% endif %}
            </ul>
            
            <p><strong>Action requise :</strong> Veuillez finaliser cette mission ou mettre à jour son statut immédiatement.</p>
            
            <a href="{{ frontend_url }}/missions/{{ mission.id }}" class="button">Voir la mission</a>
        </div>
        
        <div class="footer">
            <p>Ceci est un email automatique du système RH. Ne pas répondre à cet email.</p>
            <p>Vous recevez cet email car vous êtes impliqué dans cette mission.</p>
        </div>
    </div>
</body>
</html>
