server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;

    # Configuration MIME types pour JavaScript
    location ~* \.js$ {
        root   /usr/share/nginx/html;
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Configuration MIME types pour CSS
    location ~* \.css$ {
        root   /usr/share/nginx/html;
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;  # Important pour React Router

        # Ajouter l'en-tête Content-Security-Policy pour autoriser les scripts inline et les connexions API
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http://localhost:8000 http://backend:8000; font-src 'self' data: https:; connect-src 'self' http://localhost:8000 https://localhost:8000 http://backend:8000 http://localhost:9411 http://localhost:9411/api http://localhost:9411/api/v2 http://zipkin:9411 http://zipkin:9411/api http://zipkin:9411/api/v2 http://rh-system.local https://rh-system.local https://zipkin.rh-system.local https://zipkin.rh-system.local/api https://zipkin.rh-system.local/api/v2 https://zipkin.rh-system.local/zipkin https://prometheus.rh-system.local wss: ws: 'unsafe-inline';";
    }

    # Proxy pour Zipkin
    location /zipkin/ {
        proxy_pass http://zipkin:9411/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Ajouter des en-têtes CORS pour permettre les requêtes depuis le frontend
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';

        # Répondre OK aux requêtes OPTIONS
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Redirection pour les fichiers statiques (sauf JS et CSS qui ont leurs propres configurations)
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
        try_files $uri =404;
    }

    # Health check endpoint
    location = /health.txt {
        root   /usr/share/nginx/html;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
