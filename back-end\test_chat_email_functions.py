#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test complet des fonctionnalités email pour le chat avec Gmail SMTP
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')

try:
    django.setup()
    print("✅ Django initialisé avec succès")
except Exception as e:
    print(f"❌ Erreur initialisation Django: {e}")
    sys.exit(1)

from django.conf import settings
from Rh_app.models import User, Message, Notification
from Rh_app.utils import send_email_notification
from Rh_app.notification_service import notification_service

def test_message_important_email():
    """Test d'envoi d'email pour message important"""
    print("\n💬 Test d'email pour message important")
    print("-" * 50)
    
    try:
        # Simuler un message important
        subject = "Test Message Important - Gmail SMTP"
        message_content = """Bonjour,

Ceci est un test de message important du système de chat.

Le message original était : "Test de message important avec Gmail SMTP"

Veuillez vous connecter à la plateforme pour y répondre.

Cordialement,
L'équipe RH"""

        result = send_email_notification(
            subject=subject,
            message=message_content,
            recipient_list=['<EMAIL>'],
            email_type='important_message',
            reference_id=999
        )
        
        print(f"✅ Email message important envoyé ! Résultat: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur email message important: {e}")
        return False

def test_notification_service_missions():
    """Test du service de notification pour les missions"""
    print("\n📋 Test du service de notification missions")
    print("-" * 50)
    
    try:
        # Simuler une notification de mission avec le service
        from datetime import datetime, timedelta
        
        # Test d'envoi direct via notification_service
        # (simulation car nous n'avons pas de vraies missions en base)
        
        # Test de la méthode d'envoi d'email de mouvement d'employé
        admin_user = User.objects.filter(user_type='admin').first()
        if admin_user:
            notification_service.send_employee_movement_notification(
                user=admin_user,
                movement_type='test',
                details="Test de notification de mouvement avec Gmail SMTP"
            )
            print("✅ Notification de mouvement envoyée !")
        else:
            print("⚠️ Aucun utilisateur admin trouvé pour le test")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur service notification: {e}")
        return False

def test_chat_api_important_message():
    """Test de l'API de chat avec message important"""
    print("\n🔗 Test API chat avec message important")
    print("-" * 50)
    
    try:
        # D'abord, obtenir un token d'authentification
        login_url = "http://localhost:8000/auth/login/"
        login_data = {
            "username": "wael",
            "password": "Abidos$$123"
        }
        
        login_response = requests.post(
            login_url,
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec de connexion: {login_response.status_code}")
            return False
        
        token_data = login_response.json()
        token = token_data.get('access')
        
        if not token:
            print("❌ Token non reçu")
            return False
        
        print(f"✅ Token obtenu: {token[:20]}...")
        
        # Maintenant, envoyer un message important
        message_url = "http://localhost:8000/messages/"
        
        # Trouver un destinataire (admin)
        admin_user = User.objects.filter(user_type='admin').first()
        if not admin_user:
            print("❌ Aucun utilisateur admin trouvé")
            return False
        
        message_data = {
            "recipient": admin_user.id,
            "content": "Test de message important avec Gmail SMTP via API",
            "is_important": True
        }
        
        message_response = requests.post(
            message_url,
            json=message_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            },
            timeout=30
        )
        
        print(f"Status Code: {message_response.status_code}")
        print(f"Response: {message_response.text}")
        
        if message_response.status_code == 201:
            print("✅ Message important envoyé via API !")
            print("📧 Email automatique envoyé au destinataire")
            return True
        else:
            print(f"❌ Erreur API message: {message_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur API chat: {e}")
        return False

def test_notification_creation():
    """Test de création de notifications en base"""
    print("\n🔔 Test de création de notifications")
    print("-" * 50)
    
    try:
        # Compter les notifications existantes
        initial_count = Notification.objects.count()
        print(f"Notifications existantes: {initial_count}")
        
        # Créer une notification de test
        admin_user = User.objects.filter(user_type='admin').first()
        if admin_user:
            notification = Notification.objects.create(
                user=admin_user,
                title="Test Notification Gmail SMTP",
                message="Test de création de notification avec le nouveau système Gmail SMTP",
                type="info"
            )
            
            final_count = Notification.objects.count()
            print(f"Notifications après création: {final_count}")
            print(f"✅ Notification créée avec ID: {notification.id}")
            return True
        else:
            print("❌ Aucun utilisateur admin trouvé")
            return False
        
    except Exception as e:
        print(f"❌ Erreur création notification: {e}")
        return False

def test_message_model():
    """Test du modèle Message"""
    print("\n📝 Test du modèle Message")
    print("-" * 50)
    
    try:
        # Compter les messages existants
        initial_count = Message.objects.count()
        print(f"Messages existants: {initial_count}")
        
        # Vérifier les messages importants
        important_count = Message.objects.filter(is_important=True).count()
        print(f"Messages importants: {important_count}")
        
        # Vérifier les messages non lus
        unread_count = Message.objects.filter(is_read=False).count()
        print(f"Messages non lus: {unread_count}")
        
        print("✅ Modèle Message vérifié !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur modèle Message: {e}")
        return False

def run_all_chat_tests():
    """Exécuter tous les tests de chat"""
    print("🚀 TEST COMPLET DES FONCTIONNALITÉS EMAIL POUR LE CHAT")
    print("=" * 65)
    
    tests = [
        ("Message Important Email", test_message_important_email),
        ("Notification Service Missions", test_notification_service_missions),
        ("Chat API Message Important", test_chat_api_important_message),
        ("Notification Creation", test_notification_creation),
        ("Message Model", test_message_model),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 65)
    print("📊 RÉSUMÉ DES TESTS CHAT")
    print("=" * 65)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<45} {status}")
        if result:
            passed += 1
    
    print("-" * 65)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS CHAT RÉUSSIS !")
        print("💬 Système de chat entièrement opérationnel avec Gmail SMTP")
        print("📧 Messages importants envoyés par email")
        print("🔔 Notifications en temps réel fonctionnelles")
    else:
        print("⚠️ Certains tests ont échoué")
        print("🔧 Vérifiez la configuration et les logs")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_chat_tests()
    sys.exit(0 if success else 1)
