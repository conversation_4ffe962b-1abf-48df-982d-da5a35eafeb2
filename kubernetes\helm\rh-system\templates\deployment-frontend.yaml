{{- if .Values.frontend.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.frontend.name }}
  namespace: rh-system
  labels:
    app: {{ .Values.frontend.name }}
spec:
  replicas: {{ .Values.frontend.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.frontend.name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{ .Values.frontend.name }}
    spec:
      containers:
      - name: {{ .Values.frontend.name }}
        image: "{{ .Values.frontend.image.repository }}:{{ .Values.frontend.image.tag }}"
        imagePullPolicy: {{ .Values.frontend.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.frontend.service.port }}
          name: http
        resources:
          {{- toYaml .Values.frontend.resources | nindent 10 }}
        env:
        - name: VITE_API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: rh-frontend-config
              key: API_BASE_URL
        readinessProbe:
          httpGet:
            path: /
            port: {{ .Values.frontend.service.port }}
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: {{ .Values.frontend.service.port }}
          initialDelaySeconds: 20
          periodSeconds: 15
      restartPolicy: Always
{{- end }}
