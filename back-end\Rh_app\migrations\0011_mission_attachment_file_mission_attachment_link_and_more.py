# Generated by Django 5.2 on 2025-05-04 02:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0010_jobapplication_password'),
    ]

    operations = [
        migrations.AddField(
            model_name='mission',
            name='attachment_file',
            field=models.FileField(blank=True, null=True, upload_to='mission_attachments/'),
        ),
        migrations.AddField(
            model_name='mission',
            name='attachment_link',
            field=models.URLField(blank=True, help_text='Lien vers un document Google Drive ou GitHub', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='mission',
            name='completed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='mission',
            name='completion_link',
            field=models.URLField(blank=True, help_text="Lien fourni par l'employé lors de la complétion", max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='mission',
            name='status',
            field=models.Char<PERSON>ield(choices=[('pending', 'En attente'), ('completed', 'Terminée'), ('late', 'En retard')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='mission',
            name='work_duration',
            field=models.DurationField(blank=True, help_text='Durée de travail sur la mission', null=True),
        ),
    ]
