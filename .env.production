# Configuration pour la production
# Utilise les images Docker Hub

# Mode de déploiement
COMPOSE_MODE=production

# Images Docker Hub
FRONTEND_IMAGE=waelbenabid/rh-frontend:latest
BACKEND_IMAGE=waelbenabid/rh-system-backend:latest

# Ports pour la production
FRONTEND_PORT=5175
FRONTEND_INTERNAL_PORT=80
BACKEND_PORT=8000
DB_PORT=5433

# Configuration Frontend
NODE_ENV=production
VITE_API_BASE_URL=http://backend:8000
VITE_ZIPKIN_URL=http://localhost:9411
VITE_ZIPKIN_ENABLED=true

# Configuration Backend
DEBUG=False
ADMIN_USERNAME=wael
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Abidos$$123
ADMIN_FIRSTNAME=Wael
ADMIN_LASTNAME=Ben Abid

# Base de données
DATABASE_NAME=rh_v2
DATABASE_USER=postgres
DATABASE_PASSWORD=12345
DATABASE_HOST=db
DATABASE_PORT=5432
POSTGRES_DB=rh_v2
POSTGRES_USER=postgres
POSTGRES_PASSWORD=12345

# Configuration Gmail SMTP
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_PROVIDER=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=vyjv svsb cdvk aixv
DEFAULT_FROM_EMAIL=RH System <<EMAIL>>

# Zipkin
ZIPKIN_ENABLED=True
ZIPKIN_SERVER_URL=http://zipkin:9411
ZIPKIN_SERVICE_NAME=rh-backend
ZIPKIN_SAMPLE_RATE=100.0

# Ancienne configuration SendGrid (supprimée - maintenant utilise Gmail SMTP)
# SENDGRID_API_KEY=*********************************************************************

# Frontend URL
FRONTEND_URL=http://localhost:5175
