{{- if .Values.postgresql.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.postgresql.name }}
  namespace: rh-system
  labels:
    app: {{ .Values.postgresql.name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.postgresql.name }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.postgresql.name }}
    spec:
      containers:
      - name: postgres
        image: "{{ .Values.postgresql.image.repository }}:{{ .Values.postgresql.image.tag }}"
        imagePullPolicy: {{ .Values.postgresql.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.postgresql.service.port }}
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: postgres-config
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: postgres-config
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_PASSWORD
        resources:
          {{- toYaml .Values.postgresql.resources | nindent 10 }}
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-pvc
{{- end }}
