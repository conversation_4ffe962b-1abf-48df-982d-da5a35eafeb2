apiVersion: batch/v1
kind: Job
metadata:
  name: postgres-manual-backup
  namespace: rh-system
spec:
  template:
    spec:
      containers:
      - name: postgres-backup
        image: busybox:1.36
        command:
        - /bin/sh
        - -c
        - |
          echo "Simulating backup process..."
          echo "Creating backup file..."
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)
          echo "This is a test backup created at $TIMESTAMP" > /backup/postgres_rh_v2_$TIMESTAMP.sql.gz
          echo "Backup completed successfully!"
          ls -la /backup
        volumeMounts:
        - name: backup-volume
          mountPath: /backup
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      restartPolicy: OnFailure
      volumes:
      - name: backup-volume
        persistentVolumeClaim:
          claimName: postgres-backup-pvc
