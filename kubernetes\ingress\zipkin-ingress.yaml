apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    # Réécriture spécifique pour Zipkin API
    nginx.ingress.kubernetes.io/rewrite-target: /$2
  name: zipkin-api-ingress
  namespace: rh-system
spec:
  ingressClassName: nginx
  rules:
  - host: rh-system.local
    http:
      paths:
      # Route spécifique pour l'API Zipkin avec réécriture
      - path: /zipkin(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: zipkin
            port:
              number: 9411
