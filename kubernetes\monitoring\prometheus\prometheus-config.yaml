apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: rh-system
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        monitor: 'rh-system-monitor'

    # Alertmanager configuration
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          # - alertmanager:9093

    # Load rules once and periodically evaluate them
    rule_files:
      - /etc/prometheus/rules/*.yml

    # Scrape configurations
    scrape_configs:
      # Scrape Prometheus itself
      - job_name: 'prometheus'
        static_configs:
        - targets: ['localhost:9090']

      # Scrape Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

      # Scrape node metrics (disabled for development environment)
      # - job_name: 'kubernetes-nodes'
      #   scheme: https
      #   tls_config:
      #     ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      #     insecure_skip_verify: true
      #   bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      #   kubernetes_sd_configs:
      #   - role: node
      #   relabel_configs:
      #   - action: labelmap
      #     regex: __meta_kubernetes_node_label_(.+)
      #   - target_label: __address__
      #     replacement: kubernetes.default.svc:443
      #   - source_labels: [__meta_kubernetes_node_name]
      #     regex: (.+)
      #     target_label: __metrics_path__
      #     replacement: /v1/nodes/${1}/proxy/metrics

      # Scrape PostgreSQL exporter (disabled - service not deployed)
      # - job_name: 'postgres-exporter'
      #   static_configs:
      #   - targets: ['postgres-exporter:9187']

      # Scrape Backend metrics
      - job_name: 'rh-backend'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_app]
          action: keep
          regex: rh-system;rh-backend
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name

      # Scrape Frontend metrics (disabled - nginx doesn't serve prometheus metrics)
      # - job_name: 'rh-frontend'
      #   kubernetes_sd_configs:
      #   - role: pod
      #   relabel_configs:
      #   - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_app]
      #     action: keep
      #     regex: rh-system;rh-frontend

      # Scrape Zipkin metrics
      - job_name: 'zipkin'
        scrape_interval: 15s
        metrics_path: '/prometheus'
        static_configs:
          - targets: ['zipkin.rh-system.svc.cluster.local:9411']
