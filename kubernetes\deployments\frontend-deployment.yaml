apiVersion: apps/v1
kind: Deployment
metadata:
  name: rh-frontend
  namespace: rh-system
  labels:
    app: rh-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rh-frontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "false"
      labels:
        app: rh-frontend
    spec:
      containers:
      - name: rh-frontend
        image: waelbenabid/rh-frontend:v2.2-topnet-images
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            cpu: "50m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
        env:
        - name: VITE_API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: rh-frontend-config
              key: API_BASE_URL
        - name: VITE_ZIPKIN_URL
          valueFrom:
            configMapKeyRef:
              name: rh-frontend-config
              key: VITE_ZIPKIN_URL
        - name: VITE_ZIPKIN_ENABLED
          valueFrom:
            configMapKeyRef:
              name: rh-frontend-config
              key: VITE_ZIPKIN_ENABLED

      restartPolicy: Always
