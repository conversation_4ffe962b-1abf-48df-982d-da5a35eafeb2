apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rh-frontend-hpa
  namespace: rh-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rh-frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
