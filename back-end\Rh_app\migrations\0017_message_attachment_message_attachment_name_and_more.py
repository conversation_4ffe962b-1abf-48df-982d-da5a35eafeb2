# Generated by Django 5.0.2 on 2025-05-15 00:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0016_message'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='attachment',
            field=models.FileField(blank=True, null=True, upload_to='message_attachments/'),
        ),
        migrations.AddField(
            model_name='message',
            name='attachment_name',
            field=models.CharField(blank=True, help_text='Nom original du fichier', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='attachment_type',
            field=models.CharField(blank=True, help_text='Type MIME du fichier attaché', max_length=50, null=True),
        ),
    ]
