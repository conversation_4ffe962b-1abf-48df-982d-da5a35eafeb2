apiVersion: v1
kind: ConfigMap
metadata:
  name: rh-backend-config
  namespace: rh-system
data:
  DEBUG: "False"
  ALLOWED_HOSTS: "localhost,127.0.0.1,rh-backend,rh-frontend,rh-system.local,***********,************,*"
  DATABASE_NAME: "rh_v2"
  DATABASE_USER: "postgres"
  DATABASE_HOST: "postgres-db"
  DATABASE_PORT: "5432"
  FRONTEND_URL: "https://rh-system.local"
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:80,http://localhost,http://127.0.0.1:80,http://127.0.0.1,http://frontend,http://frontend:80,https://rh-system.local,http://rh-system.local"
  CSRF_TRUSTED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:80,http://localhost,http://127.0.0.1:80,http://127.0.0.1,http://frontend,http://frontend:80,https://rh-system.local,http://rh-system.local"

  # Configuration Zipkin
  ZIPKIN_ENABLED: "True"
  ZIPKIN_SERVER_URL: "http://zipkin.rh-system.svc.cluster.local:9411"
  ZIPKIN_SERVICE_NAME: "rh-backend"
  ZIPKIN_SAMPLE_RATE: "100.0"

  # Configuration Email - Gmail SMTP
  EMAIL_BACKEND: "django.core.mail.backends.smtp.EmailBackend"
  EMAIL_PROVIDER: "gmail"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "True"
  DEFAULT_FROM_EMAIL: "RH System <<EMAIL>>"
