{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true}, "include": ["src", "shared"], "references": [{"path": "./tsconfig.node.json"}]}