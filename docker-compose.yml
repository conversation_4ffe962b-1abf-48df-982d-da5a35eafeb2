services:
  frontend:
    image: ${FRONTEND_IMAGE:-waelbenabid/rh-frontend:latest}
    build:
      context: ./project
      dockerfile: ${FRONTEND_DOCKERFILE:-Dockerfile}
    ports:
      - "${FRONTEND_PORT:-5175}:${FRONTEND_INTERNAL_PORT:-80}"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://backend:8000}
      - VITE_ZIPKIN_URL=${VITE_ZIPKIN_URL:-/zipkin}
      - VITE_ZIPKIN_ENABLED=${VITE_ZIPKIN_ENABLED:-true}
    depends_on:
      - backend
      - zipkin
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:80/health.txt || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: ${BACKEND_IMAGE:-waelbenabid/rh-system-backend:latest}
    build:
      context: ./back-end
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    env_file:
      - ./back-end/.env
    environment:
      - ADMIN_USERNAME=${ADMIN_USERNAME:-wael}
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-Abidos$$123}
      - ADMIN_FIRSTNAME=${ADMIN_FIRSTNAME:-Wael}
      - ADMIN_LASTNAME=${ADMIN_LASTNAME:-Ben Abid}
      - DEBUG=${DEBUG:-True}
      - DATABASE_NAME=${DATABASE_NAME:-rh_v2}
      - DATABASE_USER=${DATABASE_USER:-postgres}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-12345}
      - DATABASE_HOST=${DATABASE_HOST:-db}
      - DATABASE_PORT=${DATABASE_PORT:-5432}
      # Configuration Email - Gmail SMTP
      - EMAIL_BACKEND=${EMAIL_BACKEND:-django.core.mail.backends.smtp.EmailBackend}
      - EMAIL_PROVIDER=${EMAIL_PROVIDER:-gmail}
      - EMAIL_HOST=${EMAIL_HOST:-smtp.gmail.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS:-True}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL:-RH System <<EMAIL>>}
      # Configuration Zipkin
      - ZIPKIN_ENABLED=${ZIPKIN_ENABLED:-True}
      - ZIPKIN_SERVER_URL=${ZIPKIN_SERVER_URL:-http://zipkin:9411}
      - ZIPKIN_SERVICE_NAME=${ZIPKIN_SERVICE_NAME:-rh-backend}
      - ZIPKIN_SAMPLE_RATE=${ZIPKIN_SAMPLE_RATE:-100.0}
      # Ancienne configuration SendGrid (optionnelle)
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:5175}
      # Les informations sensibles comme DATABASE_PASSWORD, SECRET_KEY et SENDGRID_API_KEY
      # doivent être définies dans le fichier .env, pas ici
    volumes:
      - ./back-end:/app
      # Assurez-vous que les répertoires de médias et statiques persistent
      - backend_media:/app/media_v2
      - backend_static:/app/static
    networks:
      - app-network
    restart: unless-stopped
    depends_on:
      - db
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Utiliser le script entrypoint.sh pour démarrer l'application
    entrypoint: ["/bin/bash", "/app/entrypoint.sh"]

  db:
    image: postgres:15-alpine
    ports:
      - "${DB_PORT:-5433}:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-rh_v2}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-12345}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  zipkin:
    image: openzipkin/zipkin:latest
    ports:
      - "9411:9411"
    environment:
      - STORAGE_TYPE=mem
      - JAVA_OPTS=-Xms512m -Xmx512m
      # Activer CORS pour permettre les requêtes depuis le frontend
      - ZIPKIN_UI_CORS_ALLOWED_ORIGINS=*
      - ZIPKIN_UI_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - ZIPKIN_UI_CORS_ALLOWED_HEADERS=Origin,X-Requested-With,Content-Type,Accept,Authorization
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9411/health"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  backend_media:
  backend_static:
