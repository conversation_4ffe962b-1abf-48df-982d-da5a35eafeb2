apiVersion: v1
kind: Namespace
metadata:
  name: kyverno
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: kyverno
  namespace: kyverno
spec:
  interval: 5m
  chart:
    spec:
      chart: kyverno
      version: "2.7.4"
      sourceRef:
        kind: HelmRepository
        name: kyverno
        namespace: flux-system
  values:
    replicaCount: 1
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
