#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test des fonctionnalités Gmail SMTP dans Minikube
"""

import requests
import json
import time

def test_minikube_backend_health():
    """Test de santé du backend Minikube"""
    print("🏥 Test de santé du backend Minikube")
    print("=" * 50)
    
    try:
        # Test via NodePort
        response = requests.get("http://192.168.49.2:30749/health/", timeout=10)
        
        if response.status_code == 200:
            print("✅ Backend Minikube accessible via ingress")
            print(f"   Status: {response.status_code}")
            return True
        else:
            print(f"❌ Erreur backend: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur connexion backend: {e}")
        return False

def test_minikube_forgot_password():
    """Test de réinitialisation de mot de passe dans Minikube"""
    print("\n🔑 Test de réinitialisation de mot de passe Minikube")
    print("=" * 60)
    
    try:
        # Données pour la réinitialisation
        reset_data = {
            "email": "<EMAIL>"
        }
        
        print("1. Envoi de demande de réinitialisation...")
        response = requests.post(
            "http://127.0.0.1/api/forgot-password/",
            json=reset_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("   ✅ Demande de réinitialisation envoyée !")
            print(f"   Message: {response_data.get('message', 'N/A')}")
            print("   📧 Email de réinitialisation envoyé via Gmail SMTP")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Détails: {error_data}")
            except:
                print(f"   Réponse brute: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test réinitialisation: {e}")
        return False

def test_minikube_job_application():
    """Test de candidature dans Minikube"""
    print("\n📋 Test de candidature Minikube")
    print("=" * 40)
    
    try:
        # Données de candidature
        application_data = {
            "application_type": "employee",
            "position": "Développeur Full Stack - Test Minikube Gmail SMTP",
            "first_name": "Jean",
            "last_name": "Dupont",
            "username": "jean.dupont.minikube",
            "email": "<EMAIL>",
            "phone": "+33 1 23 45 67 89",
            "education": "Master en Informatique",
            "experience": "3 ans d'expérience en développement web",
            "motivation": "Passionné par le développement et désireux de rejoindre votre équipe pour contribuer aux projets innovants dans Minikube.",
            "password": "TestMinikube123!",
            "github_profile": "https://github.com/jeandupont"
        }
        
        print("1. Envoi de candidature via Minikube...")
        print(f"   Poste: {application_data['position']}")
        print(f"   Candidat: {application_data['first_name']} {application_data['last_name']}")
        print(f"   Email: {application_data['email']}")
        
        # Créer un fichier CV factice
        cv_content = b"CV de Jean Dupont - Developpeur Full Stack Minikube\nExperience: 3 ans\nCompetences: Python, Django, React, Kubernetes, etc."
        
        # Préparer les données multipart
        files = {
            'cv_file': ('cv_jean_dupont_minikube.txt', cv_content, 'text/plain')
        }
        
        # Envoyer la candidature
        response = requests.post(
            "http://127.0.0.1/api/job-applications/",
            data=application_data,
            files=files,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"   ✅ Candidature créée avec ID: {response_data.get('id')}")
            print("   📧 Emails automatiques envoyés via Gmail SMTP :")
            print("      - Confirmation au candidat")
            print("      - Notification aux administrateurs")
            return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Détails: {error_data}")
            except:
                print(f"   Réponse brute: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test candidature: {e}")
        return False

def test_minikube_frontend():
    """Test de l'interface frontend dans Minikube"""
    print("\n🌐 Test de l'interface frontend Minikube")
    print("=" * 50)
    
    try:
        # Test de la page d'accueil
        response = requests.get("http://127.0.0.1/", timeout=10)
        
        if response.status_code == 200:
            print("✅ Frontend Minikube accessible")
            print(f"   Status: {response.status_code}")
            print("   Interface web disponible via ingress")
            return True
        else:
            print(f"❌ Erreur frontend: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur connexion frontend: {e}")
        return False

def run_all_minikube_tests():
    """Exécuter tous les tests Minikube"""
    print("🚀 TESTS COMPLETS GMAIL SMTP DANS MINIKUBE")
    print("=" * 70)
    print("🔗 URL d'accès: http://127.0.0.1 (via minikube tunnel)")
    print("☸️ Cluster: Minikube")
    print("📧 Email Provider: Gmail SMTP")
    print("🐳 Image: waelbenabid/rh-system-backend:gmail-smtp")
    print("=" * 70)
    
    tests = [
        ("Santé Backend", test_minikube_backend_health),
        ("Interface Frontend", test_minikube_frontend),
        ("Réinitialisation Mot de Passe", test_minikube_forgot_password),
        ("Candidature Complète", test_minikube_job_application),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: RÉUSSI")
            else:
                print(f"❌ {test_name}: ÉCHOUÉ")
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
        
        # Pause entre les tests
        time.sleep(2)
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS MINIKUBE GMAIL SMTP")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS MINIKUBE RÉUSSIS !")
        print("📧 Vérifiez votre boîte email Gmail")
        print("🚀 Système RH entièrement opérationnel dans Minikube avec Gmail SMTP")
        print("☸️ Déploiement Kubernetes validé")
    else:
        print("⚠️ Certains tests ont échoué")
        print("🔧 Vérifiez la configuration et les logs Kubernetes")
    
    print("\n🌟 INFORMATIONS D'ACCÈS:")
    print("   Frontend: http://127.0.0.1")
    print("   Backend API: http://127.0.0.1/api/")
    print("   Grafana: http://127.0.0.1/grafana/")
    print("   Zipkin: http://127.0.0.1/zipkin/")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_minikube_tests()
    exit(0 if success else 1)
