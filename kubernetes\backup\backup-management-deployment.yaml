apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-backup-management
  namespace: rh-system
  labels:
    app: postgres-backup-management
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-backup-management
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: postgres-backup-management
    spec:
      containers:
      - name: postgres-backup-management
        image: busybox:1.36
        command:
        - /bin/sh
        - -c
        - |
          echo "Backup management pod is running"
          touch /backup/pod-ready
          echo "Pod is ready"
          while true; do
            sleep 3600
          done
        volumeMounts:
        - name: backup-volume
          mountPath: /backup
        - name: backup-scripts
          mountPath: /scripts
        env:
        - name: POSTGRES_PASSWORD
          value: "postgres"
        - name: AZURE_STORAGE_KEY
          value: "default-key-for-development-only"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
        readinessProbe:
          exec:
            command:
            - cat
            - /backup/pod-ready
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: backup-volume
        persistentVolumeClaim:
          claimName: postgres-backup-pvc
      - name: backup-scripts
        configMap:
          name: postgres-backup-scripts
          defaultMode: 0755
