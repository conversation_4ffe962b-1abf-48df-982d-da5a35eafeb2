#!/usr/bin/env python
"""
Script de test pour vérifier la configuration email Gmail SMTP
Usage: python test_email.py
"""

import os
import sys
import django
from django.conf import settings

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SystemeRH_v2.settings')
django.setup()

from django.core.mail import send_mail
from django.core.mail import EmailMessage

def test_email_configuration():
    """Test de la configuration email"""
    print("🔧 Configuration Email actuelle:")
    print(f"   EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"   EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', 'Non configuré')}")
    print(f"   EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'Non configuré')}")
    print(f"   EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Non configuré')}")
    print(f"   EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', 'Non configuré')}")
    print(f"   DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()

def send_test_email(recipient_email):
    """Envoyer un email de test"""
    try:
        print(f"📧 Envoi d'un email de test vers {recipient_email}...")
        
        # Email simple
        send_mail(
            subject='Test RH System - Configuration Gmail SMTP',
            message='Ceci est un email de test pour vérifier la configuration Gmail SMTP du système RH.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            fail_silently=False,
        )
        
        print("✅ Email envoyé avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi de l'email: {e}")
        return False

def send_html_test_email(recipient_email):
    """Envoyer un email HTML de test"""
    try:
        print(f"📧 Envoi d'un email HTML de test vers {recipient_email}...")
        
        html_content = """
        <html>
        <body>
            <h2>🎉 Test RH System - Gmail SMTP</h2>
            <p>Félicitations ! La configuration Gmail SMTP fonctionne parfaitement.</p>
            <ul>
                <li>✅ Connexion SMTP établie</li>
                <li>✅ Authentification réussie</li>
                <li>✅ Email HTML supporté</li>
            </ul>
            <p><strong>Votre système RH est prêt à envoyer des notifications !</strong></p>
            <hr>
            <small>Email généré automatiquement par le système RH</small>
        </body>
        </html>
        """
        
        email = EmailMessage(
            subject='✅ Test RH System - Gmail SMTP Configuré',
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[recipient_email],
        )
        email.content_subtype = "html"
        email.send()
        
        print("✅ Email HTML envoyé avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi de l'email HTML: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test de configuration Gmail SMTP pour RH System")
    print("=" * 50)
    
    # Afficher la configuration
    test_email_configuration()
    
    # Demander l'email de test
    if len(sys.argv) > 1:
        test_email = sys.argv[1]
    else:
        test_email = input("📧 Entrez votre email pour le test: ").strip()
    
    if not test_email:
        print("❌ Email requis pour le test")
        sys.exit(1)
    
    print(f"🎯 Envoi des emails de test vers: {test_email}")
    print()
    
    # Tests
    success_count = 0
    
    # Test 1: Email simple
    if send_test_email(test_email):
        success_count += 1
    
    print()
    
    # Test 2: Email HTML
    if send_html_test_email(test_email):
        success_count += 1
    
    print()
    print("=" * 50)
    print(f"📊 Résultats: {success_count}/2 tests réussis")
    
    if success_count == 2:
        print("🎉 Configuration Gmail SMTP parfaitement fonctionnelle !")
    else:
        print("⚠️  Vérifiez votre configuration Gmail SMTP")
        print("💡 Assurez-vous d'avoir:")
        print("   - Activé l'authentification à 2 facteurs")
        print("   - Généré un mot de passe d'application")
        print("   - Utilisé le bon email et mot de passe")
