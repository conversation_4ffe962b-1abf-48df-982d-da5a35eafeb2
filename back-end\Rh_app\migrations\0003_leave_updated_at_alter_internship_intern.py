# Generated by Django 5.2 on 2025-04-29 17:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Rh_app', '0002_user_approved'),
    ]

    operations = [
        migrations.AddField(
            model_name='leave',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='internship',
            name='intern',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='internships', to=settings.AUTH_USER_MODEL),
        ),
    ]
